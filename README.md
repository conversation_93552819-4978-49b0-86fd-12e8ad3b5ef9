# 🌍 ResQTech - Real-time Disaster Monitoring & Management System

ResQTech is an advanced **🚨 disaster monitoring and management platform** for India, offering **real-time updates, community support, and emergency assistance.** Our mission is to provide accurate and timely information to help people stay safe during disasters.

---

## 🚀 Key Features

✅ **📡 Live Disaster Monitoring & Alerts** - Stay informed with **real-time** disaster updates.\
✅ **🌍 Interactive Disaster Map** - View disaster-prone areas on an intuitive **map-based dashboard.**\
✅ **🤝 Community Help System** - Connect with others and **report disasters effortlessly.**\
✅ **📖 Disaster Mitigation Guides** - Learn **how to prepare and respond** to disasters.\
✅ **🚑 Emergency Services Directory** - Access **helpline numbers and essential resources.**\
✅ **🌤 Real-time Weather Updates** - Get the **latest forecasts** and disaster warnings.

---

## 🛠 Tech Stack
## 🛠 Tech Stack

![React](https://img.shields.io/badge/React-%2361DAFB.svg?style=plastic&logo=react&logoColor=black)
![Tailwind CSS](https://img.shields.io/badge/TailwindCSS-%2338B2AC.svg?style=plastic&logo=tailwind-css&logoColor=white)
![Leaflet Maps](https://img.shields.io/badge/Leaflet%20Maps-%230A793E.svg?style=plastic&logo=leaflet&logoColor=white)
![Firebase](https://img.shields.io/badge/Firebase-%23FFCA28.svg?style=plastic&logo=firebase&logoColor=black)
![JavaScript](https://img.shields.io/badge/JavaScript-%23F7DF1E.svg?style=plastic&logo=javascript&logoColor=black)
![Node.js](https://img.shields.io/badge/Node.js-%23339933.svg?style=plastic&logo=node.js&logoColor=white)
![Express.js](https://img.shields.io/badge/Express.js-%23404d59.svg?style=plastic&logo=express&logoColor=white)
![APIs](https://img.shields.io/badge/Multiple%20APIs-%23007EC6.svg?style=plastic&logo=api&logoColor=white)

---

## 🏗️ Installation & Setup

### 1️⃣ Clone the Repository

```bash
 git clone https://github.com/YOUR_USERNAME/resqtech.git
```

### 2️⃣ Install Dependencies

```bash
 cd resqtech
 npm install
```

### 3️⃣ Start Development Server

```bash
 npm start
```

---

## 🔑 Environment Variables

Create a `` file in the root directory and add the following keys:

```plaintext
REACT_APP_OPENWEATHER_API_KEY=your_key
REACT_APP_NASA_API_KEY=your_key
REACT_APP_NOAA_API_KEY=your_key
```

(Replace `your_key` with actual API keys.)

---

## 🤝 Contributing

🔹 Pull requests are **welcome!** For major changes, open an issue first to discuss the proposal.\
🔹 Follow the project's **coding guidelines and best practices.**\
🔹 Make sure to **test your changes** before submitting a PR.

---

## 📜 License

This project is licensed under the [MIT License](https://choosealicense.com/licenses/mit/).

📩 **Need help?** Contact us at [<EMAIL>](mailto\:<EMAIL>)\
🌟 **Star this repo** if you find it useful! 🚀


