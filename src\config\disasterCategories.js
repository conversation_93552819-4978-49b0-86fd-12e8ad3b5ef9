export const DISASTER_CATEGORIES = {
  'Natural Disasters': [
    { value: 'flood', label: 'Flood', keywords: ['water', 'flooding', 'submerged', 'inundated', 'overflow'] },
    { value: 'earthquake', label: 'Earthquake', keywords: ['rubble', 'crack', 'destruction', 'collapsed', 'shaking'] },
    { value: 'landslide', label: 'Landslide', keywords: ['debris', 'mud', 'rocks', 'slope', 'fallen'] },
    { value: 'cyclone', label: 'Cyclone', keywords: ['storm', 'wind', 'debris', 'damage', 'destroyed'] }
  ],
  'Weather Conditions': [
    { value: 'heavy_rain', label: 'Heavy Rain', keywords: ['rain', 'water', 'flood', 'downpour', 'wet'] },
    { value: 'heatwave', label: 'Heat Wave', keywords: ['sun', 'hot', 'dry', 'parched', 'scorched'] },
    { value: 'storm', label: 'Storm', keywords: ['lightning', 'thunder', 'wind', 'dark', 'rain'] }
  ],
  'Urban Incidents': [
    { value: 'fire', label: 'Fire', keywords: ['fire', 'smoke', 'burning', 'flame', 'ash'] },
    { value: 'building_collapse', label: 'Building Collapse', keywords: ['rubble', 'debris', 'structure', 'fallen', 'crushed'] },
    { value: 'road_damage', label: 'Road Damage', keywords: ['crack', 'hole', 'damage', 'broken', 'surface'] }
  ]
};