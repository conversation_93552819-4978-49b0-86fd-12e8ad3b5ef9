import React, { useState } from "react";
import { NavLink, useNavigate } from "react-router-dom";
import { Menu, X } from "lucide-react";
import TranslatableText from "./TranslatableText";
import LanguageSelector from "./LanguageSelector";
import ThemeToggle from "./ThemeToggle";
import { useTheme } from "../contexts/ThemeContext";

function Header() {
  const [isOpen, setIsOpen] = useState(false);
  const { darkMode } = useTheme();
  const navigate = useNavigate();

  return (
    <header className={`w-full ${darkMode ? 'bg-gray-900' : 'bg-yellow-400'} shadow-lg relative z-50`}>
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <h1 className={`text-2xl font-bold ${darkMode ? 'text-yellow-400' : 'text-black'}`}>
              <TranslatableText>ResQTech</TranslatableText>
            </h1>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <NavLink
              to="/"
              className={({ isActive }) =>
                `font-medium transition-colors ${
                  isActive
                    ? (darkMode ? 'text-yellow-400' : 'text-black')
                    : (darkMode ? 'text-gray-300 hover:text-yellow-400' : 'text-gray-700 hover:text-black')
                }`
              }
            >
              <TranslatableText>HOME</TranslatableText>
            </NavLink>
            <NavLink
              to="/home"
              className={({ isActive }) =>
                `font-medium transition-colors ${
                  isActive
                    ? (darkMode ? 'text-yellow-400' : 'text-black')
                    : (darkMode ? 'text-gray-300 hover:text-yellow-400' : 'text-gray-700 hover:text-black')
                }`
              }
            >
              <TranslatableText>NEWS</TranslatableText>
            </NavLink>
            <NavLink
              to="/relocation"
              className={({ isActive }) =>
                `font-medium transition-colors ${
                  isActive
                    ? (darkMode ? 'text-yellow-400' : 'text-black')
                    : (darkMode ? 'text-gray-300 hover:text-yellow-400' : 'text-gray-700 hover:text-black')
                }`
              }
            >
              <TranslatableText>MAP</TranslatableText>
            </NavLink>
            <NavLink
              to="/about"
              className={({ isActive }) =>
                `font-medium transition-colors ${
                  isActive
                    ? (darkMode ? 'text-yellow-400' : 'text-black')
                    : (darkMode ? 'text-gray-300 hover:text-yellow-400' : 'text-gray-700 hover:text-black')
                }`
              }
            >
              <TranslatableText>CONTACT</TranslatableText>
            </NavLink>
          </nav>

          {/* Right side buttons and controls */}
          <div className="hidden md:flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <LanguageSelector />
              <ThemeToggle />
            </div>
            <button
              onClick={() => navigate("/community-help")}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                darkMode
                  ? 'text-gray-300 hover:text-white hover:bg-gray-800'
                  : 'text-gray-700 hover:text-black hover:bg-yellow-300'
              }`}
            >
              <TranslatableText>Sign In</TranslatableText>
            </button>
            <button
              onClick={() => navigate("/donation")}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                darkMode
                  ? 'bg-yellow-600 text-white hover:bg-yellow-700'
                  : 'bg-black text-white hover:bg-gray-800'
              }`}
            >
              <TranslatableText>Register</TranslatableText>
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className={`md:hidden p-2 rounded-lg ${
              darkMode ? 'text-yellow-400' : 'text-black'
            }`}
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className={`md:hidden mt-4 pb-4 ${darkMode ? 'bg-gray-800' : 'bg-yellow-300'} rounded-lg`}>
            <nav className="flex flex-col space-y-2 p-4">
              <NavLink
                to="/"
                className={`py-2 px-4 rounded font-medium transition-colors ${
                  darkMode ? 'text-gray-300 hover:text-yellow-400 hover:bg-gray-700' : 'text-gray-700 hover:text-black hover:bg-yellow-200'
                }`}
                onClick={() => setIsOpen(false)}
              >
                <TranslatableText>HOME</TranslatableText>
              </NavLink>
              <NavLink
                to="/home"
                className={`py-2 px-4 rounded font-medium transition-colors ${
                  darkMode ? 'text-gray-300 hover:text-yellow-400 hover:bg-gray-700' : 'text-gray-700 hover:text-black hover:bg-yellow-200'
                }`}
                onClick={() => setIsOpen(false)}
              >
                <TranslatableText>NEWS</TranslatableText>
              </NavLink>
              <NavLink
                to="/relocation"
                className={`py-2 px-4 rounded font-medium transition-colors ${
                  darkMode ? 'text-gray-300 hover:text-yellow-400 hover:bg-gray-700' : 'text-gray-700 hover:text-black hover:bg-yellow-200'
                }`}
                onClick={() => setIsOpen(false)}
              >
                <TranslatableText>MAP</TranslatableText>
              </NavLink>
              <NavLink
                to="/about"
                className={`py-2 px-4 rounded font-medium transition-colors ${
                  darkMode ? 'text-gray-300 hover:text-yellow-400 hover:bg-gray-700' : 'text-gray-700 hover:text-black hover:bg-yellow-200'
                }`}
                onClick={() => setIsOpen(false)}
              >
                <TranslatableText>CONTACT</TranslatableText>
              </NavLink>
              <div className="border-t border-gray-600 pt-4 mt-4">
                <div className="flex flex-col space-y-2">
                  <LanguageSelector />
                  <ThemeToggle />
                </div>
                <div className="flex flex-col space-y-2 mt-4">
                  <button
                    onClick={() => {
                      navigate("/community-help");
                      setIsOpen(false);
                    }}
                    className={`py-2 px-4 rounded font-medium transition-colors ${
                      darkMode ? 'text-gray-300 hover:text-white hover:bg-gray-700' : 'text-gray-700 hover:text-black hover:bg-yellow-200'
                    }`}
                  >
                    <TranslatableText>Sign In</TranslatableText>
                  </button>
                  <button
                    onClick={() => {
                      navigate("/donation");
                      setIsOpen(false);
                    }}
                    className={`py-2 px-4 rounded font-medium transition-colors ${
                      darkMode ? 'bg-yellow-600 text-white hover:bg-yellow-700' : 'bg-black text-white hover:bg-gray-800'
                    }`}
                  >
                    <TranslatableText>Register</TranslatableText>
                  </button>
                </div>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}

export default Header;
