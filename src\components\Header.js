import React, { useState, useCallback } from "react";
import { NavLink, useNavigate, useLocation } from "react-router-dom";
import { Menu, X } from "lucide-react";
import TranslatableText from "./TranslatableText";
import LanguageSelector from "./LanguageSelector";
import ThemeToggle from "./ThemeToggle";
import { useTheme } from "../contexts/ThemeContext";

// Navigation items matching Google Maps style - moved outside component for performance
const NAVIGATION_ITEMS = [
  { path: "/", label: "Welcome", icon: "🏠" },
  { path: "/home", label: "Disasters", icon: "🌪️" },
  { path: "/relocation", label: "Relocation", icon: "📍" },
  { path: "/community-help", label: "Alerts", icon: "🚨" },
  { path: "/mitigation", label: "Mitigation", icon: "🛡️" },
  { path: "/about", label: "Helplines", icon: "📞" },
  { path: "/donation", label: "Donations", icon: "💝" },
];

const Header = React.memo(() => {
  const [isOpen, setIsOpen] = useState(false);
  const { darkMode } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();

  const isActiveRoute = useCallback((path) => {
    if (path === "/" && location.pathname === "/") return true;
    if (path !== "/" && location.pathname.startsWith(path)) return true;
    return false;
  }, [location.pathname]);



  return (
    <header className={`w-full ${darkMode ? 'bg-gray-900/95' : 'bg-white/95'} backdrop-blur-sm border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} shadow-sm sticky top-0 z-50`}>
      <div className="container mx-auto px-4 py-2">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <h1 className={`text-xl font-bold ${darkMode ? 'text-yellow-400' : 'text-gray-900'} cursor-pointer`}
                onClick={() => navigate("/")}>
              <TranslatableText>ResQTech</TranslatableText>
            </h1>
          </div>

          {/* Google Maps Style Navigation Pills */}
          <nav className="hidden lg:flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-full p-1 shadow-inner nav-container">
            {NAVIGATION_ITEMS.map((item) => (
              <NavLink
                key={item.path}
                to={item.path}
                className={`nav-pill relative px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 flex items-center space-x-2 ${
                  isActiveRoute(item.path)
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-md nav-active'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-white/70 dark:hover:bg-gray-700/70 hover:shadow-sm'
                }`}
              >
                <span className="text-base transition-transform duration-200 hover:scale-110">{item.icon}</span>
                <span className="font-medium"><TranslatableText>{item.label}</TranslatableText></span>
                {isActiveRoute(item.path) && (
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 pointer-events-none animate-pulse" />
                )}
              </NavLink>
            ))}
          </nav>

          {/* Right side controls */}
          <div className="hidden lg:flex items-center space-x-3">
            <LanguageSelector />
            <ThemeToggle />
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden flex items-center space-x-2">
            <LanguageSelector />
            <ThemeToggle />
            <button
              onClick={() => setIsOpen(!isOpen)}
              className={`p-2 rounded-md ${darkMode ? 'text-gray-300 hover:text-white hover:bg-gray-800' : 'text-gray-700 hover:text-black hover:bg-gray-100'}`}
            >
              {isOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className={`lg:hidden mt-4 pb-4 ${darkMode ? 'bg-gray-800/95' : 'bg-white/95'} backdrop-blur-sm rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'} shadow-lg`}>
            <nav className="flex flex-col space-y-1 p-3">
              {NAVIGATION_ITEMS.map((item) => (
                <NavLink
                  key={item.path}
                  to={item.path}
                  className={`py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center space-x-3 ${
                    isActiveRoute(item.path)
                      ? `${darkMode ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-900'} shadow-sm`
                      : `${darkMode ? 'text-gray-300 hover:text-white hover:bg-gray-700' : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'}`
                  }`}
                  onClick={() => setIsOpen(false)}
                >
                  <span className="text-lg">{item.icon}</span>
                  <span><TranslatableText>{item.label}</TranslatableText></span>
                  {isActiveRoute(item.path) && (
                    <div className="ml-auto w-2 h-2 bg-blue-500 rounded-full"></div>
                  )}
                </NavLink>
              ))}
            </nav>
          </div>
        )}
      </div>
    </header>
  );
});

Header.displayName = 'Header';

export default Header;
