import React from "react";
import Header from "../components/Header";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import TranslatableText from "../components/TranslatableText";

function Welcome() {
  const navigate = useNavigate();
  // const { darkMode } = useTheme(); // Removed for now as we're using fixed styling

  return (
    <div className="min-h-screen bg-gray-100">
      <Header />

      {/* Hero Section with collage background */}
      <section className="disaster-collage relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background image collage */}
        <div className="absolute inset-0 grid grid-cols-6 grid-rows-4 opacity-70">
          <div className="bg-cover bg-center transform rotate-2" style={{ backgroundImage: "url('/assam1.jpeg')" }}></div>
          <div className="bg-cover bg-center transform -rotate-1" style={{ backgroundImage: "url('/gujarat1.jpeg')" }}></div>
          <div className="bg-cover bg-center transform rotate-1" style={{ backgroundImage: "url('/kerala1.jpg')" }}></div>
          <div className="bg-cover bg-center transform -rotate-2" style={{ backgroundImage: "url('/odisha1.jpeg')" }}></div>
          <div className="bg-cover bg-center transform rotate-1" style={{ backgroundImage: "url('/uttarakhand1.jpeg')" }}></div>
          <div className="bg-cover bg-center transform -rotate-1" style={{ backgroundImage: "url('/resqbg.jpg')" }}></div>

          <div className="bg-cover bg-center transform -rotate-1" style={{ backgroundImage: "url('/assam2.jpeg')" }}></div>
          <div className="bg-cover bg-center transform rotate-2" style={{ backgroundImage: "url('/gujarat2.jpeg')" }}></div>
          <div className="bg-cover bg-center transform -rotate-2" style={{ backgroundImage: "url('/kerala2.jpg')" }}></div>
          <div className="bg-cover bg-center transform rotate-1" style={{ backgroundImage: "url('/kerala3.jpeg')" }}></div>
          <div className="bg-cover bg-center transform -rotate-1" style={{ backgroundImage: "url('/odisha2.jpg')" }}></div>
          <div className="bg-cover bg-center transform rotate-2" style={{ backgroundImage: "url('/uttarakhand2.jpg')" }}></div>

          <div className="bg-cover bg-center transform rotate-1" style={{ backgroundImage: "url('/assam1.jpeg')" }}></div>
          <div className="bg-cover bg-center transform -rotate-2" style={{ backgroundImage: "url('/gujarat1.jpeg')" }}></div>
          <div className="bg-cover bg-center transform rotate-2" style={{ backgroundImage: "url('/kerala1.jpg')" }}></div>
          <div className="bg-cover bg-center transform -rotate-1" style={{ backgroundImage: "url('/odisha1.jpeg')" }}></div>
          <div className="bg-cover bg-center transform rotate-1" style={{ backgroundImage: "url('/uttarakhand1.jpeg')" }}></div>
          <div className="bg-cover bg-center transform -rotate-2" style={{ backgroundImage: "url('/resqbg.jpg')" }}></div>

          <div className="bg-cover bg-center transform -rotate-1" style={{ backgroundImage: "url('/assam2.jpeg')" }}></div>
          <div className="bg-cover bg-center transform rotate-1" style={{ backgroundImage: "url('/gujarat2.jpeg')" }}></div>
          <div className="bg-cover bg-center transform -rotate-2" style={{ backgroundImage: "url('/kerala2.jpg')" }}></div>
          <div className="bg-cover bg-center transform rotate-2" style={{ backgroundImage: "url('/kerala3.jpeg')" }}></div>
          <div className="bg-cover bg-center transform -rotate-1" style={{ backgroundImage: "url('/odisha2.jpg')" }}></div>
          <div className="bg-cover bg-center transform rotate-1" style={{ backgroundImage: "url('/uttarakhand2.jpg')" }}></div>
        </div>

        {/* Enhanced dark overlay with gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-black/40 to-black/60"></div>

        <div className="hero-content container mx-auto px-4 py-20">
          <div className="flex flex-col lg:flex-row items-center justify-center gap-20">

            {/* Left side - Text content */}
            <motion.div
              className="lg:w-1/2 text-center"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.h1
                className="resqtech-title text-7xl lg:text-8xl xl:text-9xl mb-16 leading-none"
                style={{
                  background: "linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FFD700 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                  filter: "drop-shadow(3px 3px 6px rgba(0,0,0,0.5))",
                }}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 1 }}
              >
                <TranslatableText>Welcome to ResQTech</TranslatableText>
              </motion.h1>

              <motion.div
                className="flex flex-col sm:flex-row gap-8 justify-center mt-8"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8, duration: 0.8 }}
              >
                <motion.button
                  whileHover={{ scale: 1.08, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="resqtech-button px-12 py-5 bg-yellow-500 text-black rounded-full text-xl shadow-2xl hover:bg-yellow-400 transition-all duration-300 border-3 border-yellow-600"
                  style={{
                    boxShadow: "0 10px 25px rgba(255, 193, 7, 0.4), 0 0 0 1px rgba(255, 193, 7, 0.1)",
                  }}
                  onClick={() => navigate("/home")}
                >
                  <TranslatableText>Latest updates</TranslatableText>
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.08, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="resqtech-button px-12 py-5 bg-transparent border-3 border-white text-white rounded-full text-xl hover:bg-white hover:text-black transition-all duration-300 shadow-2xl"
                  style={{
                    boxShadow: "0 10px 25px rgba(255, 255, 255, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1)",
                  }}
                  onClick={() => navigate("/relocation")}
                >
                  <TranslatableText>Disaster Map</TranslatableText>
                </motion.button>
              </motion.div>
            </motion.div>

            {/* Right side - Phone mockup */}
            <motion.div
              className="lg:w-1/2 flex justify-center lg:justify-end"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <motion.div
                className="relative rounded-[50px] overflow-hidden shadow-2xl border-[12px] border-black"
                style={{
                  width: "320px",
                  height: "640px",
                  boxShadow: "0 30px 60px -12px rgba(0, 0, 0, 0.8)",
                }}
                initial={{ y: 30 }}
                animate={{ y: [0, -15, 0] }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut",
                }}
              >
                {/* Status bar */}
                <div className="absolute top-0 left-0 right-0 bg-black text-white px-6 pt-3 pb-2 flex items-center justify-between text-sm z-20 rounded-t-[38px]">
                  <div className="flex items-center space-x-1">
                    <div className="flex space-x-1">
                      <div className="w-1 h-1 bg-white rounded-full"></div>
                      <div className="w-1 h-1 bg-white rounded-full"></div>
                      <div className="w-1 h-1 bg-white rounded-full"></div>
                      <div className="w-1 h-1 bg-white rounded-full"></div>
                    </div>
                  </div>
                  <div className="font-semibold">9:41</div>
                  <div className="flex items-center space-x-1">
                    <div className="flex space-x-1">
                      <div className="w-5 h-3 border border-white rounded-sm">
                        <div className="w-4/5 h-full bg-white rounded-sm"></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Map interface */}
                <div className="w-full h-full bg-gray-200 flex flex-col pt-12">
                  {/* Map content */}
                  <div className="flex-grow relative bg-green-100 overflow-hidden">
                    {/* Map background */}
                    <div className="absolute inset-0 bg-gradient-to-br from-green-200 via-blue-100 to-green-300"></div>

                    {/* India map container */}
                    <div className="relative w-full h-full">
                      {/* India SVG map */}
                      <div className="w-full h-full flex items-center justify-center p-6">
                        <img
                          src="/india.svg"
                          alt="India Map"
                          className="w-full h-full object-contain opacity-80"
                          style={{
                            filter: "drop-shadow(0px 4px 8px rgba(0,0,0,0.2))",
                          }}
                        />
                      </div>

                      {/* Red location marker - centered */}
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div className="w-8 h-8 bg-red-500 rounded-full border-3 border-white shadow-xl flex items-center justify-center animate-pulse">
                          <div className="w-3 h-3 bg-white rounded-full"></div>
                        </div>
                      </div>

                      {/* Map controls */}
                      <div className="absolute top-6 right-6 bg-white rounded-xl shadow-lg p-2">
                        <div className="flex flex-col space-y-2">
                          <button className="w-10 h-10 bg-white rounded-lg shadow flex items-center justify-center hover:bg-gray-50">
                            <svg className="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                          </button>
                          <button className="w-10 h-10 bg-white rounded-lg shadow flex items-center justify-center hover:bg-gray-50">
                            <svg className="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* What is ResQtech and About us sections */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-8">

            {/* What is ResQtech */}
            <motion.div
              className="relative group cursor-pointer"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
            >
              <div
                className="h-96 rounded-3xl relative overflow-hidden shadow-2xl"
                style={{
                  backgroundImage: `linear-gradient(rgba(255, 193, 7, 0.85), rgba(255, 152, 0, 0.85)), url('/gujarat1.jpeg')`,
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }}
              >
                <div className="absolute inset-0 flex items-center justify-center">
                  <h2 className="text-5xl font-black text-black text-center leading-tight drop-shadow-lg">
                    <TranslatableText>What is ResQtech</TranslatableText>
                  </h2>
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>
            </motion.div>

            {/* About us */}
            <motion.div
              className="relative group cursor-pointer"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
            >
              <div
                className="h-96 rounded-3xl relative overflow-hidden shadow-2xl"
                style={{
                  backgroundImage: `linear-gradient(rgba(255, 193, 7, 0.85), rgba(255, 152, 0, 0.85)), url('/kerala1.jpg')`,
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }}
              >
                <div className="absolute inset-0 flex items-center justify-center">
                  <h2 className="text-5xl font-black text-black text-center leading-tight drop-shadow-lg">
                    <TranslatableText>About us</TranslatableText>
                  </h2>
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default Welcome;
