import React from "react";
import Header from "../components/Header";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import TranslatableText from "../components/TranslatableText";
import { useTheme } from "../contexts/ThemeContext";

function Welcome() {
  const navigate = useNavigate();
  const { darkMode } = useTheme();

  return (
    <div className={`min-h-screen ${darkMode ? "bg-gray-900 text-white" : "bg-gray-50 text-gray-900"}`}>
      <Header />

      {/* Hero Section */}
      <section
        className="relative min-h-screen flex items-center justify-center"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('/resqbg.jpg')`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className="container mx-auto px-4 py-20">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-12">

            {/* Left side - Text content */}
            <motion.div
              className="lg:w-1/2 text-center lg:text-left"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.h1
                className="text-5xl lg:text-7xl font-bold mb-8 text-yellow-400"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.8 }}
              >
                <TranslatableText>Welcome to ResQTech</TranslatableText>
              </motion.h1>

              <motion.div
                className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.8 }}
              >
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-8 py-4 bg-yellow-500 text-black rounded-full font-semibold text-lg shadow-lg hover:bg-yellow-400 transition-colors"
                  onClick={() => navigate("/home")}
                >
                  <TranslatableText>Latest updates</TranslatableText>
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-8 py-4 bg-transparent border-2 border-white text-white rounded-full font-semibold text-lg hover:bg-white hover:text-black transition-colors"
                  onClick={() => navigate("/relocation")}
                >
                  <TranslatableText>Disaster Map</TranslatableText>
                </motion.button>
              </motion.div>
            </motion.div>

            {/* Right side - Phone mockup */}
            <motion.div
              className="lg:w-1/2 flex justify-center"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div
                className="relative rounded-[40px] overflow-hidden shadow-2xl border-[8px] border-gray-900"
                style={{
                  width: "280px",
                  height: "580px",
                  boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.5)",
                }}
                initial={{ y: 20 }}
                animate={{ y: [0, -10, 0] }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut",
                }}
              >
                {/* Status bar */}
                <div className="absolute top-0 left-0 right-0 bg-transparent text-white px-4 pt-2 pb-1 flex items-center justify-between text-sm z-10">
                  <div className="flex items-center space-x-1">
                    <div className="flex space-x-1">
                      <div className="w-1 h-1 bg-white rounded-full"></div>
                      <div className="w-1 h-1 bg-white rounded-full"></div>
                      <div className="w-1 h-1 bg-white rounded-full"></div>
                    </div>
                  </div>
                  <div className="font-medium">9:41</div>
                  <div className="flex items-center space-x-1">
                    <div className="flex space-x-1">
                      <div className="w-4 h-2 border border-white rounded-sm">
                        <div className="w-full h-full bg-white rounded-sm"></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Map interface */}
                <div className="w-full h-full bg-gray-100 flex flex-col">
                  {/* Map content */}
                  <div className="flex-grow relative bg-sky-100 overflow-hidden">
                    {/* India map container */}
                    <div className="relative w-full h-full">
                      {/* India SVG map */}
                      <div className="w-full h-full flex items-center justify-center p-4">
                        <img
                          src="/india.svg"
                          alt="India Map"
                          className="w-full h-full object-contain"
                          style={{
                            filter: "drop-shadow(0px 2px 4px rgba(0,0,0,0.1))",
                          }}
                        />
                      </div>

                      {/* Red location marker */}
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div className="w-6 h-6 bg-red-500 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                        <div className="absolute top-6 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold whitespace-nowrap">
                          Disaster Zone
                        </div>
                      </div>

                      {/* Map controls */}
                      <div className="absolute top-4 right-4 bg-white rounded-lg shadow-md p-1">
                        <div className="flex flex-col space-y-1">
                          <button className="w-8 h-8 bg-white rounded shadow flex items-center justify-center">
                            <svg className="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                          </button>
                          <button className="w-8 h-8 bg-white rounded shadow flex items-center justify-center">
                            <svg className="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* What is ResQtech and About us sections */}
      <section className={`py-20 ${darkMode ? "bg-gray-800" : "bg-white"}`}>
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12">

            {/* What is ResQtech */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div
                className="h-80 rounded-2xl relative overflow-hidden"
                style={{
                  backgroundImage: `linear-gradient(rgba(255, 193, 7, 0.8), rgba(255, 193, 7, 0.8)), url('/gujarat1.jpeg')`,
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }}
              >
                <div className="absolute inset-0 flex items-center justify-center">
                  <h2 className="text-4xl font-bold text-black">
                    <TranslatableText>What is ResQtech</TranslatableText>
                  </h2>
                </div>
              </div>
            </motion.div>

            {/* About us */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div
                className="h-80 rounded-2xl relative overflow-hidden"
                style={{
                  backgroundImage: `linear-gradient(rgba(255, 193, 7, 0.8), rgba(255, 193, 7, 0.8)), url('/kerala1.jpg')`,
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }}
              >
                <div className="absolute inset-0 flex items-center justify-center">
                  <h2 className="text-4xl font-bold text-black">
                    <TranslatableText>About us</TranslatableText>
                  </h2>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default Welcome;
