import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Circle,
  Polyline,
} from "react-leaflet";

import "leaflet/dist/leaflet.css";
import { safeLocations as safeLocationsData } from "../data/safeLocations";
import Header from "../components/Header"; // Import Header component

import TranslatableText from "../components/TranslatableText"; // Import TranslatableText component
import { useTheme } from "../contexts/ThemeContext"; // Import ThemeContext

// Add debounce utility
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Add this function at the top level
const fetchNearbyHospitals = async (lat, lon, radius = 5000) => {
  try {
    const query = `
      [out:json][timeout:25];
      (
        node["amenity"="hospital"](around:${radius},${lat},${lon});
        way["amenity"="hospital"](around:${radius},${lat},${lon});
        relation["amenity"="hospital"](around:${radius},${lat},${lon});
      );
      out body;
      >;
      out skel qt;
    `;

    const response = await fetch("https://overpass-api.de/api/interpreter", {
      method: "POST",
      body: query,
    });

    const data = await response.json();
    return data.elements.map((element) => ({
      id: element.id,
      name: element.tags?.name || "Unnamed Hospital",
      coordinates: [element.lat, element.lon],
      type: element.tags?.healthcare || "Hospital",
      emergency: element.tags?.emergency || "yes",
      phone: element.tags?.phone || "N/A",
      address:
        element.tags?.["addr:full"] ||
        element.tags?.["addr:street"] ||
        "Address not available",
    }));
  } catch (error) {
    console.error("Error fetching nearby hospitals:", error);
    return [];
  }
};

function Relocation() {
  const [locations, setLocations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [userLocation, setUserLocation] = useState(null);
  const [nearestSafeZone, setNearestSafeZone] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [locationSearch, setLocationSearch] = useState("");

  const [travelDetails, setTravelDetails] = useState(null);
  const [emergencyMode, setEmergencyMode] = useState(false);
  const { darkMode } = useTheme();

  // Move calculateTravelDetails to the top of component
  const calculateTravelDetails = useCallback((from, to) => {
    const distance = Math.round(to.distance);
    let recommendedMode;
    let estimatedTime;
    let route;

    if (distance > 700) {
      recommendedMode = "air";
      estimatedTime = Math.ceil(distance / 800);
      route = {
        primary: to.transportInfo.nearestAirport,
        steps: [
          "Get to nearest airport",
          "Take flight to destination",
          "Local transport to safe zone",
        ],
      };
    } else if (distance > 300) {
      recommendedMode = "rail";
      estimatedTime = Math.ceil(distance / 60);
      route = {
        primary: to.transportInfo.nearestStation,
        steps: [
          "Head to nearest railway station",
          "Take train to destination",
          "Local transport to safe zone",
        ],
      };
    } else {
      recommendedMode = "road";
      estimatedTime = Math.ceil(distance / 50);
      route = {
        primary: to.transportInfo.majorHighways.join(", "),
        steps: [
          "Take main highway",
          "Follow road signs to destination",
          "Local transport to safe zone",
        ],
      };
    }

    const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&origin=${from[0]},${from[1]}&destination=${to.coordinates[0]},${to.coordinates[1]}`;

    return {
      distance,
      recommendedMode,
      estimatedTime,
      route,
      googleMapsUrl,
    };
  }, []);

  // Function to calculate distance between two points using Haversine formula
  const calculateDistance = useCallback((lat1, lon1, lat2, lon2) => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLon = ((lon2 - lon1) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((lat1 * Math.PI) / 180) *
        Math.cos((lat2 * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }, []);

  // Get user's location
  const getUserLocation = () => {
    if (navigator.geolocation) {
      setLoading(true);
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const userCoords = [
            position.coords.latitude,
            position.coords.longitude,
          ];
          setUserLocation(userCoords);

          // Get location name using reverse geocoding
          try {
            const response = await fetch(
              `https://nominatim.openstreetmap.org/reverse?format=json&lat=${userCoords[0]}&lon=${userCoords[1]}&addressdetails=1`
            );
            const data = await response.json();

            const nearest = locations.reduce((closest, loc) => {
              const distance = calculateDistance(
                userCoords[0],
                userCoords[1],
                loc.coordinates[0],
                loc.coordinates[1]
              );
              if (!closest || distance < closest.distance) {
                return { ...loc, distance: parseFloat(distance.toFixed(1)) };
              }
              return closest;
            }, null);

            if (nearest) {
              setNearestSafeZone({
                ...nearest,
                userState: data.address?.state || "Your Location",
              });
              const details = calculateTravelDetails(userCoords, nearest);
              setTravelDetails(details);
            }
          } catch (error) {
            console.error("Error getting location details:", error);
          }
          setLoading(false);
        },
        (error) => {
          console.error("Error getting location:", error);
          setLoading(false);
        }
      );
    }
  };

  // Add travel details calculation function

  // Update the handleLocationSearch function
  const handleLocationSearch = useCallback(
    async (searchQuery) => {
      if (!searchQuery) return;

      try {
        setLoading(true);
        // 1. First get coordinates of searched location
        const locationResponse = await fetch(
          `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
            searchQuery
          )}, India&countrycodes=in&limit=1&addressdetails=1`
        );
        const locationData = await locationResponse.json();

        if (locationData.length > 0) {
          const location = locationData[0];
          const searchedCoords = [
            parseFloat(location.lat),
            parseFloat(location.lon),
          ];
          setUserLocation(searchedCoords);

          // 2. Fetch hospitals near the searched location
          const hospitalsQuery = `
        [out:json][timeout:25];
        (
          node["amenity"="hospital"](around:10000,${searchedCoords[0]},${searchedCoords[1]});
          way["amenity"="hospital"](around:10000,${searchedCoords[0]},${searchedCoords[1]});
          relation["amenity"="hospital"](around:10000,${searchedCoords[0]},${searchedCoords[1]});
        );
        out body;
        >;
        out skel qt;
      `;

          const hospitalsResponse = await fetch(
            "https://overpass-api.de/api/interpreter",
            {
              method: "POST",
              body: hospitalsQuery,
            }
          );

          const hospitalsData = await hospitalsResponse.json();

          // 3. Process hospital data
          const nearbyHospitals = hospitalsData.elements
            .filter((element) => element.tags && element.tags.name)
            .map((hospital) => ({
              id: hospital.id,
              name: hospital.tags.name,
              coordinates: [hospital.lat, hospital.lon],
              type: hospital.tags.healthcare || "Hospital",
              emergency: hospital.tags.emergency || "yes",
              phone:
                hospital.tags.phone || hospital.tags["contact:phone"] || "N/A",
              address:
                hospital.tags["addr:full"] ||
                hospital.tags["addr:street"] ||
                "Address not available",
              distance: calculateDistance(
                searchedCoords[0],
                searchedCoords[1],
                hospital.lat,
                hospital.lon
              ).toFixed(1),
            }))
            .sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance));

          // 4. Find nearest safe zone
          const nearest = locations.reduce((closest, loc) => {
            const distance = calculateDistance(
              searchedCoords[0],
              searchedCoords[1],
              loc.coordinates[0],
              loc.coordinates[1]
            );
            if (!closest || distance < closest.distance) {
              return { ...loc, distance: parseFloat(distance.toFixed(1)) };
            }
            return closest;
          }, null);

          if (nearest) {
            setNearestSafeZone({
              ...nearest,
              userState: location.address?.state || "Unknown Location",
              searchedLocation: {
                name: location.display_name,
                coordinates: searchedCoords,
              },
              nearbyHospitals: nearbyHospitals.slice(0, 5), // Show top 5 nearest hospitals
            });

            const details = calculateTravelDetails(searchedCoords, nearest);
            setTravelDetails(details);

            // Log found hospitals for debugging
            console.log(
              `Found ${nearbyHospitals.length} hospitals near ${searchQuery}`
            );
          }
        }
      } catch (error) {
        console.error("Error searching location:", error);
      } finally {
        setLoading(false);
      }
    },
    [locations, calculateTravelDetails, calculateDistance]
  );

  // Add debounce to search input
  const debouncedSearch = useCallback(
    (value) => {
      const handler = debounce(
        (searchVal) => handleLocationSearch(searchVal),
        300
      );
      handler(value);
    },
    [handleLocationSearch]
  );

  useEffect(() => {
    const loadSafeLocations = async () => {
      try {
        setLoading(true);
        // Transform the safeLocations object into an array with all required properties
        const locationsArray = Object.entries(safeLocationsData).map(
          ([name, data]) => ({
            name,
            coordinates: data.coordinates,
            score: data.score,
            capacity: data.capacity,
            facilities: data.facilities,
            description: data.description,
            state: data.state,
            elevation: data.elevation,
            hasAirport: data.hasAirport,
            hasRailway: data.hasRailway,
            transportInfo: {
              nearestAirport:
                data.transportInfo?.nearestAirport || "Not available",
              nearestStation:
                data.transportInfo?.nearestStation || "Not available",
              majorHighways: data.transportInfo?.majorHighways || [],
              busTerminal: data.transportInfo?.busTerminal || "Not available",
            },
          })
        );
        setLocations(locationsArray);
      } catch (error) {
        console.error("Error loading safe locations:", error);
        setLocations([]);
      } finally {
        setLoading(false);
      }
    };

    loadSafeLocations();
  }, []);

  const filterLocations = useCallback(
    (searchTerm) => {
      if (!searchTerm) return locations;
      return locations.filter(
        (location) =>
          location.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          location.state.toLowerCase().includes(searchTerm.toLowerCase())
      );
    },
    [locations]
  );

  const filteredLocations = useMemo(
    () => filterLocations(""),
    [filterLocations]
  );
  const createHoverContent = (location) => `
    <div class="bg-white p-3 rounded shadow-lg max-w-xs">
      <h4 class="font-bold text-gray-900">${location.name.toUpperCase()}</h4>
      <div class="flex items-center mt-1">
        <div class="w-2 h-2 rounded-full mr-2 ${
          location.score >= 90
            ? "bg-green-500"
            : location.score >= 80
            ? "bg-yellow-500"
            : "bg-red-500"
        }"></div>
        <span class="text-sm text-gray-600">Safety Score: ${
          location.score
        }%</span>
      </div>
      <p class="text-sm text-gray-600 mt-1">${location.description}</p>
    </div>
  `;

  // Remove duplicate declaration of calculateTravelDetails



  // Update the LocationDetailsModal component
  const LocationDetailsModal = ({ location, onClose }) => {
    const [nearbyHospitals, setNearbyHospitals] = useState([]);
    const [loadingHospitals, setLoadingHospitals] = useState(true);

    useEffect(() => {
      const loadHospitals = async () => {
        if (location?.coordinates) {
          setLoadingHospitals(true);
          const [lat, lon] = location.coordinates;
          const hospitals = await fetchNearbyHospitals(lat, lon);
          setNearbyHospitals(hospitals);
          setLoadingHospitals(false);
        }
      };

      loadHospitals();
    }, [location]);

    if (!location) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50">
        <div
          className={`${
            darkMode ? "bg-dark-bg-secondary" : "bg-gray-800"
          } rounded-xl w-full max-w-2xl relative`}
        >
          {/* Modal Header */}
          <div
            className={`p-4 border-b ${
              darkMode ? "border-gray-700" : "border-gray-600"
            }`}
          >
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold text-white dark:text-dark-text-primary">
                {location.name.toUpperCase()}
              </h2>
              <button
                onClick={onClose}
                className={`text-gray-400 ${
                  darkMode ? "hover:text-dark-text-primary" : "hover:text-white"
                } transition-colors`}
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>

          {/* Modal Content - Scrollable */}
          <div className="overflow-y-auto p-4 max-h-[70vh] custom-scrollbar">
            {/* Safety Score */}
            <div
              className={`${
                darkMode ? "bg-dark-bg-tertiary" : "bg-gray-700"
              } p-4 rounded-lg mb-4`}
            >
              <div className="flex items-center space-x-2">
                <div
                  className={`w-3 h-3 rounded-full ${
                    location.score >= 90
                      ? "bg-green-500"
                      : location.score >= 80
                      ? "bg-yellow-500"
                      : "bg-red-500"
                  }`}
                ></div>
                <span className="text-lg font-medium text-white dark:text-dark-text-primary">
                  <TranslatableText>Safety Score:</TranslatableText>{" "}
                  {location.score}%
                </span>
              </div>
              <p className="text-gray-300 dark:text-dark-text-secondary mt-2">
                <TranslatableText>{location.description}</TranslatableText>
              </p>
            </div>

            {/* Location Details */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div
                className={`${
                  darkMode ? "bg-dark-bg-tertiary" : "bg-gray-700"
                } p-3 rounded-lg`}
              >
                <p className="text-gray-400 dark:text-dark-text-muted">
                  <TranslatableText>State</TranslatableText>
                </p>
                <p className="text-white dark:text-dark-text-primary font-medium">
                  <TranslatableText>{location.state}</TranslatableText>
                </p>
              </div>
              <div className="bg-gray-700 p-3 rounded-lg">
                <p className="text-gray-400">
                  <TranslatableText>Elevation</TranslatableText>
                </p>
                <p className="text-white font-medium">
                  <TranslatableText>{location.elevation}</TranslatableText>
                </p>
              </div>
              <div className="bg-gray-700 p-3 rounded-lg">
                <p className="text-gray-400">
                  <TranslatableText>Capacity</TranslatableText>
                </p>
                <p className="text-white font-medium">
                  <TranslatableText>{location.capacity}</TranslatableText>
                </p>
              </div>
            </div>

            {/* Transport Information */}
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-white mb-3">
                <TranslatableText>Transport Options</TranslatableText>
              </h3>
              <div className="space-y-3">
                {location.hasAirport && (
                  <div className="bg-gray-700 p-3 rounded-lg flex items-start">
                    <span className="text-2xl mr-3">✈️</span>
                    <div>
                      <p className="text-white font-medium">
                        <TranslatableText>Air Travel</TranslatableText>
                      </p>
                      <p className="text-gray-400 text-sm">
                        <TranslatableText>
                          {location.transportInfo?.nearestAirport}
                        </TranslatableText>
                      </p>
                    </div>
                  </div>
                )}
                {location.hasRailway && (
                  <div className="bg-gray-700 p-3 rounded-lg flex items-start">
                    <span className="text-2xl mr-3">🚂</span>
                    <div>
                      <p className="text-white font-medium">
                        <TranslatableText>Railway</TranslatableText>
                      </p>
                      <p className="text-gray-400 text-sm">
                        <TranslatableText>
                          {location.transportInfo?.nearestStation}
                        </TranslatableText>
                      </p>
                    </div>
                  </div>
                )}
                <div className="bg-gray-700 p-3 rounded-lg flex items-start">
                  <span className="text-2xl mr-3">🚌</span>
                  <div>
                    <p className="text-white font-medium">
                      <TranslatableText>Bus Transport</TranslatableText>
                    </p>
                    <p className="text-gray-400 text-sm">
                      <TranslatableText>
                        {location.transportInfo?.busTerminal}
                      </TranslatableText>
                    </p>
                  </div>
                </div>
                {location.transportInfo?.majorHighways?.length > 0 && (
                  <div className="bg-gray-700 p-3 rounded-lg flex items-start">
                    <span className="text-2xl mr-3">🛣️</span>
                    <div>
                      <p className="text-white font-medium">
                        <TranslatableText>Major Highways</TranslatableText>
                      </p>
                      <p className="text-gray-400 text-sm">
                        <TranslatableText>
                          {location.transportInfo.majorHighways.join(", ")}
                        </TranslatableText>
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Available Facilities */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">
                <TranslatableText>Available Facilities</TranslatableText>
              </h3>
              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex flex-wrap gap-2">
                  {location.facilities.map((facility, index) => (
                    <span
                      key={index}
                      className="bg-gray-600 text-gray-200 px-3 py-1 rounded-full text-sm"
                    >
                      <TranslatableText>{facility}</TranslatableText>
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Add Nearby Hospitals Section */}
            <div className="mt-6">
              <h3 className="text-lg font-semibold text-white mb-3">
                <span className="mr-2">🏥</span>
                <TranslatableText>Nearby Hospitals</TranslatableText>
              </h3>
              {loadingHospitals ? (
                <div className="animate-pulse space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="bg-gray-700 h-16 rounded-lg"></div>
                  ))}
                </div>
              ) : nearbyHospitals.length > 0 ? (
                <div className="space-y-3">
                  {nearbyHospitals.map((hospital) => (
                    <div
                      key={hospital.id}
                      className="bg-gray-700 p-4 rounded-lg"
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="text-white font-medium">
                            <TranslatableText>{hospital.name}</TranslatableText>
                          </h4>
                          <p className="text-gray-400 text-sm">
                            <TranslatableText>
                              {hospital.address}
                            </TranslatableText>
                          </p>
                          {hospital.phone !== "N/A" && (
                            <p className="text-gray-400 text-sm">
                              📞{" "}
                              <TranslatableText>
                                {hospital.phone}
                              </TranslatableText>
                            </p>
                          )}
                        </div>
                        <a
                          href={`https://www.google.com/maps/dir/?api=1&destination=${hospital.coordinates[0]},${hospital.coordinates[1]}&travelmode=driving`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-1 rounded flex items-center"
                        >
                          <span className="mr-1">🚗</span>
                          <TranslatableText>Directions</TranslatableText>
                        </a>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-400">
                  <TranslatableText>
                    No hospitals found in the nearby area
                  </TranslatableText>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Update the main layout structure
  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800">
        <Header transparent={true} />

        {/* Search Bar - positioned below navigation */}
        <div className="absolute top-20 left-4 z-[1000] w-80 animate-fade-in-down">
          <div className="flex flex-col gap-2">
            <button
              onClick={getUserLocation}
              className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-xl font-medium transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105 border border-white/20 text-sm"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <TranslatableText>Use My Location</TranslatableText>
            </button>

            <div className="relative">
              <input
                type="text"
                value={locationSearch}
                onChange={(e) => {
                  setLocationSearch(e.target.value);
                  debouncedSearch(e.target.value);
                }}
                placeholder="Search location..."
                className="w-full p-3 pl-10 pr-10 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-blue-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
              />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <button
                onClick={() => handleLocationSearch(locationSearch)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white p-1 transition-colors duration-200"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </div>
          </div>
      </div>

      {/* Emergency Contacts Panel - Top Right */}
      {!emergencyMode && (
        <div className="absolute top-20 right-4 z-[1000] w-72 animate-fade-in-down">
          <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl p-4">
            <h3 className="text-lg font-semibold text-red-400 mb-3 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <TranslatableText>Emergency Contacts</TranslatableText>
            </h3>
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={() => window.open('tel:100', '_self')}
                className="p-3 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-all duration-300 flex flex-col items-center text-sm"
              >
                <span className="text-lg mb-1">🚨</span>
                <TranslatableText>Police</TranslatableText>
                <span className="text-xs opacity-80">100</span>
              </button>
              <button
                onClick={() => window.open('tel:101', '_self')}
                className="p-3 bg-orange-600 hover:bg-orange-700 text-white rounded-xl transition-all duration-300 flex flex-col items-center text-sm"
              >
                <span className="text-lg mb-1">🔥</span>
                <TranslatableText>Fire</TranslatableText>
                <span className="text-xs opacity-80">101</span>
              </button>
              <button
                onClick={() => window.open('tel:108', '_self')}
                className="p-3 bg-green-600 hover:bg-green-700 text-white rounded-xl transition-all duration-300 flex flex-col items-center text-sm"
              >
                <span className="text-lg mb-1">🚑</span>
                <TranslatableText>Ambulance</TranslatableText>
                <span className="text-xs opacity-80">108</span>
              </button>
              <button
                onClick={() => window.open('tel:1070', '_self')}
                className="p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all duration-300 flex flex-col items-center text-sm"
              >
                <span className="text-lg mb-1">🌊</span>
                <TranslatableText>Disaster</TranslatableText>
                <span className="text-xs opacity-80">1070</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Family Locator Panel - Emergency Mode */}
      {emergencyMode && (
        <div className="absolute top-20 right-4 z-[1000] w-80 animate-fade-in-down">
          <div className="bg-red-900/30 backdrop-blur-xl border border-red-400/30 rounded-2xl shadow-2xl p-4">
            <h3 className="text-lg font-semibold text-red-300 mb-3 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <TranslatableText>Family Locator</TranslatableText>
            </h3>
            <div className="space-y-3">
              <div className="bg-green-600/20 border border-green-400/30 rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                    <span className="text-white font-medium">Mom</span>
                  </div>
                  <span className="text-green-300 text-sm">Safe Zone A</span>
                </div>
                <div className="text-xs text-gray-300 mt-1">Last seen: 2 min ago</div>
              </div>

              <div className="bg-yellow-600/20 border border-yellow-400/30 rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-yellow-400 rounded-full mr-2 animate-pulse"></div>
                    <span className="text-white font-medium">Dad</span>
                  </div>
                  <span className="text-yellow-300 text-sm">En Route</span>
                </div>
                <div className="text-xs text-gray-300 mt-1">Last seen: 5 min ago</div>
              </div>

              <div className="bg-red-600/20 border border-red-400/30 rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-red-400 rounded-full mr-2 animate-pulse"></div>
                    <span className="text-white font-medium">Sister</span>
                  </div>
                  <span className="text-red-300 text-sm">Unknown</span>
                </div>
                <div className="text-xs text-gray-300 mt-1">Last seen: 15 min ago</div>
              </div>

              <button className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors text-sm font-medium">
                <TranslatableText>Send Emergency Alert to All</TranslatableText>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Emergency Mode Toggle - Top Center */}
      <div className="absolute top-20 left-1/2 transform -translate-x-1/2 z-[1000] animate-fade-in-down">
        <button
          onClick={() => setEmergencyMode(!emergencyMode)}
          className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 flex items-center gap-3 shadow-lg border ${
            emergencyMode
              ? 'bg-red-600 hover:bg-red-700 text-white border-red-400'
              : 'bg-black/20 backdrop-blur-xl hover:bg-black/30 text-white border-white/20'
          }`}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <TranslatableText>{emergencyMode ? 'Exit Emergency Mode' : 'Emergency Mode'}</TranslatableText>
          {emergencyMode && <span className="animate-pulse">🚨</span>}
        </button>
      </div>

      {/* Weather Panel - Bottom Right */}
      <div className="absolute bottom-4 right-4 z-[1000] w-64 animate-fade-in-up">
        <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl p-4">
          <h3 className="text-lg font-semibold text-yellow-400 mb-3 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
            </svg>
            <TranslatableText>Weather Alert</TranslatableText>
          </h3>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-300"><TranslatableText>Condition:</TranslatableText></span>
              <span className="text-white flex items-center">
                ⛈️ <TranslatableText>Thunderstorm</TranslatableText>
              </span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-300"><TranslatableText>Temperature:</TranslatableText></span>
              <span className="text-white">28°C</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-300"><TranslatableText>Wind:</TranslatableText></span>
              <span className="text-white">45 km/h</span>
            </div>
            <div className="bg-red-600/20 border border-red-400/30 rounded-lg p-2 mt-3">
              <div className="flex items-center text-red-400 text-xs">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <TranslatableText>Severe Weather Warning</TranslatableText>
              </div>
            </div>
          </div>
        </div>
        </div>

        {/* Full Screen Map Container */}
        <div className="h-screen w-full relative overflow-hidden">
          <MapContainer
            center={userLocation || [20.5937, 78.9629]}
            zoom={userLocation ? 8 : 4}
            className="h-full w-full"
            scrollWheelZoom={true}
            zoomControl={true}
            touchZoom={true}
            dragging={true}
            tap={true}
            minZoom={3}
            maxBounds={[
              [8.4, 68.7],
              [37.6, 97.25],
            ]}
          >
            <TileLayer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>'
              className="map-tiles"
              noWrap={false}
              opacity={1}
            />
            {/* User Location Marker */}
            {userLocation && (
              <Circle
                center={userLocation}
                radius={5000}
                pathOptions={{ color: "red", fillColor: "red" }}
              >
                <Popup>
                  <TranslatableText>Your Location</TranslatableText>
                </Popup>
              </Circle>
            )}

            {/* Path to nearest safe zone */}
            {userLocation && nearestSafeZone && (
              <Polyline
                positions={[userLocation, nearestSafeZone.coordinates]}
                pathOptions={{ color: "yellow" }}
              />
            )}

            {!loading &&
              filteredLocations.map((zone) => (
                <Circle
                  key={zone.name}
                  center={zone.coordinates}
                  radius={25000}
                  pathOptions={{
                    color:
                      zone.score >= 90
                        ? "#10B981"
                        : zone.score >= 80
                        ? "#FBBF24"
                        : "#DC2626",
                    fillOpacity: 0.2,
                  }}
                  eventHandlers={{
                    mouseover: (e) => {
                      const layer = e.target;
                      layer.setStyle({
                        fillOpacity: 0.4,
                        weight: 2,
                      });
                      layer
                        .bindTooltip(createHoverContent(zone), {
                          direction: "top",
                          sticky: true,
                          offset: [0, -10],
                          opacity: 1,
                          className: "custom-tooltip",
                        })
                        .openTooltip();
                    },
                    mouseout: (e) => {
                      const layer = e.target;
                      layer.setStyle({
                        fillOpacity: 0.2,
                        weight: 1,
                      });
                      layer.unbindTooltip();
                    },
                    click: () => {
                      // Keep the existing popup behavior
                    },
                  }}
                >
                  <Popup maxWidth={350} className="custom-popup">
                    <div className="bg-white rounded-lg p-4">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">
                        {zone.name.toUpperCase()}
                      </h3>

                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center">
                          <div
                            className={`w-3 h-3 rounded-full mr-2 ${
                              zone.score >= 90
                                ? "bg-green-500"
                                : zone.score >= 80
                                ? "bg-yellow-500"
                                : "bg-red-500"
                            }`}
                          ></div>
                          <span className="font-medium text-gray-700">
                            Safety Score: {zone.score}%
                          </span>
                        </div>
                        <div className="flex items-center text-sm">
                          <div className={`w-2 h-2 rounded-full mr-1 ${Math.random() > 0.3 ? 'bg-green-500' : 'bg-red-500'}`}></div>
                          <span className="text-gray-600">
                            {Math.random() > 0.3 ? 'Available' : 'Full'}
                          </span>
                        </div>
                      </div>

                      {/* Capacity Information */}
                      <div className="bg-gray-50 rounded-lg p-3 mb-3">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium text-gray-700">
                            <TranslatableText>Current Occupancy</TranslatableText>
                          </span>
                          <span className="text-sm text-gray-600">
                            {Math.floor(Math.random() * 80 + 10)}% Full
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${Math.random() > 0.5 ? 'bg-green-500' : 'bg-yellow-500'}`}
                            style={{width: `${Math.floor(Math.random() * 80 + 10)}%`}}
                          ></div>
                        </div>
                      </div>

                      {/* Resource Availability */}
                      <div className="grid grid-cols-3 gap-2 mb-3">
                        <div className="text-center p-2 bg-green-50 rounded">
                          <div className="text-lg">🍽️</div>
                          <div className="text-xs text-green-700">Food</div>
                          <div className="text-xs font-medium">Available</div>
                        </div>
                        <div className="text-center p-2 bg-blue-50 rounded">
                          <div className="text-lg">💧</div>
                          <div className="text-xs text-blue-700">Water</div>
                          <div className="text-xs font-medium">Available</div>
                        </div>
                        <div className="text-center p-2 bg-red-50 rounded">
                          <div className="text-lg">🏥</div>
                          <div className="text-xs text-red-700">Medical</div>
                          <div className="text-xs font-medium">Limited</div>
                        </div>
                      </div>

                      <div className="text-gray-600 mb-3 text-sm">
                        <p className="mb-1">
                          <span className="font-medium">
                            <TranslatableText>State:</TranslatableText>
                          </span>{" "}
                          <TranslatableText>{zone.state}</TranslatableText>
                        </p>
                        <p className="mb-1">
                          <span className="font-medium">
                            <TranslatableText>Distance:</TranslatableText>
                          </span>{" "}
                          {userLocation ? Math.round(Math.random() * 50 + 5) : '--'} km
                        </p>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <button
                          onClick={() => setSelectedLocation(zone)}
                          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-lg transition-colors text-sm"
                        >
                          <TranslatableText>Full Details</TranslatableText>
                        </button>
                        <button
                          onClick={() => window.open(`https://www.google.com/maps/dir/?api=1&destination=${zone.coordinates[0]},${zone.coordinates[1]}`, '_blank')}
                          className="bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded-lg transition-colors text-sm"
                        >
                          <TranslatableText>Get Directions</TranslatableText>
                        </button>
                      </div>
                    </div>
                  </Popup>
                </Circle>
              ))}
          </MapContainer>
        </div>

        {/* Route Options Panel - Bottom Left */}
        {userLocation && nearestSafeZone && (
          <div className="absolute bottom-4 left-4 z-[1000] w-80 animate-fade-in-up">
          <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl p-4">
            <h3 className="text-lg font-semibold text-blue-400 mb-3 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7" />
              </svg>
              <TranslatableText>Route Options</TranslatableText>
            </h3>
            <div className="space-y-2">
              <button
                onClick={() => window.open(`https://www.google.com/maps/dir/${userLocation[0]},${userLocation[1]}/${nearestSafeZone.coordinates[0]},${nearestSafeZone.coordinates[1]}/data=!3m1!4b1!4m2!4m1!3e0`, '_blank')}
                className="w-full p-3 bg-green-600 hover:bg-green-700 text-white rounded-xl transition-all duration-300 flex items-center justify-between text-sm"
              >
                <div className="flex items-center">
                  <span className="text-lg mr-3">🚗</span>
                  <div className="text-left">
                    <div><TranslatableText>Fastest Route</TranslatableText></div>
                    <div className="text-xs opacity-80"><TranslatableText>By Car</TranslatableText></div>
                  </div>
                </div>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </button>

              <button
                onClick={() => window.open(`https://www.google.com/maps/dir/${userLocation[0]},${userLocation[1]}/${nearestSafeZone.coordinates[0]},${nearestSafeZone.coordinates[1]}/data=!3m1!4b1!4m2!4m1!3e2`, '_blank')}
                className="w-full p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all duration-300 flex items-center justify-between text-sm"
              >
                <div className="flex items-center">
                  <span className="text-lg mr-3">🚌</span>
                  <div className="text-left">
                    <div><TranslatableText>Public Transport</TranslatableText></div>
                    <div className="text-xs opacity-80"><TranslatableText>Bus/Train</TranslatableText></div>
                  </div>
                </div>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </button>

              <button
                onClick={() => window.open(`https://www.google.com/maps/dir/${userLocation[0]},${userLocation[1]}/${nearestSafeZone.coordinates[0]},${nearestSafeZone.coordinates[1]}/data=!3m1!4b1!4m2!4m1!3e2`, '_blank')}
                className="w-full p-3 bg-purple-600 hover:bg-purple-700 text-white rounded-xl transition-all duration-300 flex items-center justify-between text-sm"
              >
                <div className="flex items-center">
                  <span className="text-lg mr-3">🚶</span>
                  <div className="text-left">
                    <div><TranslatableText>Walking Route</TranslatableText></div>
                    <div className="text-xs opacity-80"><TranslatableText>On Foot</TranslatableText></div>
                  </div>
                </div>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Offline Map Download Panel - Bottom Left (when no route) */}
      {!nearestSafeZone && (
        <div className="absolute bottom-4 left-4 z-[1000] w-80 animate-fade-in-up">
          <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl p-4">
            <h3 className="text-lg font-semibold text-purple-400 mb-3 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <TranslatableText>Offline Maps</TranslatableText>
            </h3>
            <div className="space-y-3">
              <div className="text-sm text-gray-300 mb-3">
                <TranslatableText>Download maps for offline use during emergencies</TranslatableText>
              </div>

              <button
                onClick={() => alert('Downloading current area map...')}
                className="w-full p-3 bg-purple-600 hover:bg-purple-700 text-white rounded-xl transition-all duration-300 flex items-center justify-between text-sm"
              >
                <div className="flex items-center">
                  <span className="text-lg mr-3">📍</span>
                  <div className="text-left">
                    <div><TranslatableText>Current Area</TranslatableText></div>
                    <div className="text-xs opacity-80">~25 MB</div>
                  </div>
                </div>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3" />
                </svg>
              </button>

              <button
                onClick={() => alert('Downloading state map...')}
                className="w-full p-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-xl transition-all duration-300 flex items-center justify-between text-sm"
              >
                <div className="flex items-center">
                  <span className="text-lg mr-3">🗺️</span>
                  <div className="text-left">
                    <div><TranslatableText>Entire State</TranslatableText></div>
                    <div className="text-xs opacity-80">~150 MB</div>
                  </div>
                </div>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3" />
                </svg>
              </button>

              <div className="text-xs text-gray-400 mt-2">
                <TranslatableText>Downloaded maps: 2 areas (45 MB)</TranslatableText>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Sidebar for nearest safe zone info - positioned on the right */}
      {nearestSafeZone && (
        <div className="absolute top-80 right-4 z-[1000] w-80 bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl p-4 max-h-[calc(100vh-22rem)] overflow-y-auto">
            <div className="flex justify-between items-center mb-3">
              <h2 className="text-lg font-semibold text-blue-400">
                <TranslatableText>Nearest Safe Zone</TranslatableText>
              </h2>
              <button
                onClick={() => {
                  setNearestSafeZone(null);
                  setUserLocation(null);
                  setTravelDetails(null);
                }}
                className="text-sm px-2 py-1 bg-gray-600 hover:bg-gray-500 rounded text-gray-200 flex items-center"
              >
                <svg
                  className="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
                <TranslatableText>Clear</TranslatableText>
              </button>
            </div>

            <div className="bg-gray-700 rounded-lg p-4 shadow-lg space-y-4">
              {/* Basic Info */}
              <div>
                <h3 className="font-medium text-white mb-2">
                  <TranslatableText>
                    {nearestSafeZone.name}
                  </TranslatableText>
                </h3>
                <div className="flex items-center space-x-2 mb-2">
                  <div
                    className={`w-2 h-2 rounded-full ${
                      nearestSafeZone.score >= 90
                        ? "bg-green-500"
                        : nearestSafeZone.score >= 80
                        ? "bg-yellow-500"
                        : "bg-red-500"
                    }`}
                  ></div>
                  <span className="text-sm text-gray-300">
                    <TranslatableText>Safety Score:</TranslatableText>{" "}
                    {nearestSafeZone.score}%
                  </span>
                </div>
                <div className="text-sm text-gray-400">
                  <p>
                    <TranslatableText>From:</TranslatableText>{" "}
                    <TranslatableText>
                      {nearestSafeZone.userState || "Your Location"}
                    </TranslatableText>
                  </p>
                  <p>
                    <TranslatableText>To:</TranslatableText>{" "}
                    <TranslatableText>
                      {nearestSafeZone.state}
                    </TranslatableText>
                  </p>
                  <p className="text-green-400">
                    <TranslatableText>Distance:</TranslatableText>{" "}
                    {nearestSafeZone.distance}{" "}
                    <TranslatableText>km</TranslatableText>
                  </p>
                </div>
              </div>

              {/* Travel Details */}
              {travelDetails && (
                <div className="border-t border-gray-600 pt-4">
                  <h4 className="text-sm font-semibold text-white mb-3">
                    <TranslatableText>
                      Recommended Travel Route
                    </TranslatableText>
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">
                        <TranslatableText>Best Mode:</TranslatableText>
                      </span>
                      <span className="text-white">
                        {travelDetails.recommendedMode === "air" ? (
                          <>
                            <span>✈️</span>{" "}
                            <TranslatableText>
                              Air Travel
                            </TranslatableText>
                          </>
                        ) : travelDetails.recommendedMode === "rail" ? (
                          <>
                            <span>🚂</span>{" "}
                            <TranslatableText>Railway</TranslatableText>
                          </>
                        ) : (
                          <>
                            <span>🚗</span>{" "}
                            <TranslatableText>
                              Road Transport
                            </TranslatableText>
                          </>
                        )}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">
                        <TranslatableText>
                          Est. Travel Time:
                        </TranslatableText>
                      </span>
                      <span className="text-white">
                        {travelDetails.estimatedTime}{" "}
                        <TranslatableText>hours</TranslatableText>
                      </span>
                    </div>
                    <div className="text-sm">
                      <p className="text-gray-400 mb-2">
                        <TranslatableText>Route Steps:</TranslatableText>
                      </p>
                      <ol className="list-decimal list-inside space-y-1">
                        {travelDetails.route.steps.map((step, index) => (
                          <li key={index} className="text-gray-300">
                            <TranslatableText>{step}</TranslatableText>
                          </li>
                        ))}
                      </ol>
                    </div>
                    <a
                      href={travelDetails.googleMapsUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block text-center bg-blue-600 hover:bg-blue-700 transition-colors text-white py-2 px-4 rounded-lg text-sm"
                    >
                      <TranslatableText>
                        View Route on Google Maps
                      </TranslatableText>{" "}
                      🗺️
                    </a>
                  </div>
                </div>
              )}

              <button
                className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
                onClick={() => {
                  setSelectedLocation(nearestSafeZone);
                  setShowDetails(true);
                }}
              >
                <TranslatableText>View Full Details</TranslatableText> →
              </button>
              
              {/* Add Nearby Hospitals Section */}
              {nearestSafeZone.nearbyHospitals &&
                nearestSafeZone.nearbyHospitals.length > 0 && (
                  <div className="border-t border-gray-600 pt-4 mt-4">
                    <h4 className="text-sm font-semibold text-white mb-3">
                      <span className="mr-2">🏥</span>
                      <TranslatableText>
                        Nearby Hospitals
                      </TranslatableText>
                    </h4>
                    <div className="space-y-2">
                      {nearestSafeZone.nearbyHospitals
                        .slice(0, 3)
                        .map((hospital, index) => (
                          <div
                            key={index}
                            className="bg-gray-600 rounded p-2"
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="text-sm text-white">
                                  <TranslatableText>
                                    {hospital.name}
                                  </TranslatableText>
                                </p>
                                <p className="text-xs text-gray-400">
                                  {hospital.distance}
                                  <TranslatableText>
                                    km away
                                  </TranslatableText>
                                </p>
                              </div>
                              <a
                                href={`https://www.google.com/maps/dir/?api=1&destination=${hospital.coordinates[0]},${hospital.coordinates[1]}&travelmode=driving`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-white"
                              >
                                🚗{" "}
                                <TranslatableText>Route</TranslatableText>
                              </a>
                            </div>
                            {hospital.phone !== "N/A" && (
                              <p className="text-xs text-gray-400 mt-1">
                                📞{" "}
                                <TranslatableText>
                                  {hospital.phone}
                                </TranslatableText>
                              </p>                                      )}
                          </div>
                        ))}
                    </div>
                  </div>
                )}
            </div>
          </div>
        )}

        {showDetails && (
          <LocationDetailsModal
            location={selectedLocation}
            onClose={() => {
              setShowDetails(false);
              setSelectedLocation(null);
            }}
          />
        )}
      </div>

      {/* Scrollable Content Sections Below Map */}
      <div className="bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 relative z-10">

        {/* Emergency Preparedness Section */}
        <section className="py-16 animate-fade-in-up">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-6 bg-gradient-to-r from-red-400 via-orange-400 to-yellow-400 bg-clip-text text-transparent">
                <TranslatableText>Emergency Preparedness Guide</TranslatableText>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                <TranslatableText>
                  Essential steps to prepare for emergencies and ensure your family's safety during disasters
                </TranslatableText>
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Emergency Kit */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300 group">
                <div className="text-center mb-4">
                  <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-2xl">🎒</span>
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    <TranslatableText>Emergency Kit</TranslatableText>
                  </h3>
                </div>
                <ul className="space-y-2 text-gray-300 text-sm">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                    <TranslatableText>Water (1 gallon per person per day)</TranslatableText>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                    <TranslatableText>Non-perishable food (3-day supply)</TranslatableText>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                    <TranslatableText>First aid kit and medications</TranslatableText>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                    <TranslatableText>Flashlight and extra batteries</TranslatableText>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                    <TranslatableText>Portable radio</TranslatableText>
                  </li>
                </ul>
              </div>

              {/* Communication Plan */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300 group">
                <div className="text-center mb-4">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-2xl">📞</span>
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    <TranslatableText>Communication Plan</TranslatableText>
                  </h3>
                </div>
                <ul className="space-y-2 text-gray-300 text-sm">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                    <TranslatableText>Emergency contact list</TranslatableText>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                    <TranslatableText>Meeting points for family</TranslatableText>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                    <TranslatableText>Out-of-state contact person</TranslatableText>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                    <TranslatableText>Social media emergency groups</TranslatableText>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                    <TranslatableText>Backup communication methods</TranslatableText>
                  </li>
                </ul>
              </div>

              {/* Important Documents */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300 group">
                <div className="text-center mb-4">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-2xl">📄</span>
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    <TranslatableText>Important Documents</TranslatableText>
                  </h3>
                </div>
                <ul className="space-y-2 text-gray-300 text-sm">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-400 rounded-full mr-3"></span>
                    <TranslatableText>ID cards and passports</TranslatableText>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-400 rounded-full mr-3"></span>
                    <TranslatableText>Insurance policies</TranslatableText>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-400 rounded-full mr-3"></span>
                    <TranslatableText>Bank account information</TranslatableText>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-400 rounded-full mr-3"></span>
                    <TranslatableText>Medical records</TranslatableText>
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-400 rounded-full mr-3"></span>
                    <TranslatableText>Property deeds/rental agreements</TranslatableText>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Real-time Emergency Services */}
        <section className="py-16 bg-gradient-to-r from-gray-900/50 to-slate-900/50 animate-fade-in-up">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                <TranslatableText>Emergency Services Network</TranslatableText>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                <TranslatableText>
                  Connect with emergency services and get real-time updates on available resources
                </TranslatableText>
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Police Services */}
              <div className="bg-red-900/20 backdrop-blur-xl border border-red-400/30 rounded-2xl p-6 text-center hover:bg-red-900/30 transition-all duration-300 group cursor-pointer">
                <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">🚨</span>
                </div>
                <h3 className="text-lg font-semibold text-red-300 mb-2">
                  <TranslatableText>Police Services</TranslatableText>
                </h3>
                <p className="text-red-200 text-sm mb-3">
                  <TranslatableText>Emergency: 100</TranslatableText>
                </p>
                <div className="flex items-center justify-center text-green-400 text-sm">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                  <TranslatableText>Available 24/7</TranslatableText>
                </div>
              </div>

              {/* Fire Department */}
              <div className="bg-orange-900/20 backdrop-blur-xl border border-orange-400/30 rounded-2xl p-6 text-center hover:bg-orange-900/30 transition-all duration-300 group cursor-pointer">
                <div className="w-16 h-16 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">🔥</span>
                </div>
                <h3 className="text-lg font-semibold text-orange-300 mb-2">
                  <TranslatableText>Fire Department</TranslatableText>
                </h3>
                <p className="text-orange-200 text-sm mb-3">
                  <TranslatableText>Emergency: 101</TranslatableText>
                </p>
                <div className="flex items-center justify-center text-green-400 text-sm">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                  <TranslatableText>Available 24/7</TranslatableText>
                </div>
              </div>

              {/* Medical Services */}
              <div className="bg-green-900/20 backdrop-blur-xl border border-green-400/30 rounded-2xl p-6 text-center hover:bg-green-900/30 transition-all duration-300 group cursor-pointer">
                <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">🚑</span>
                </div>
                <h3 className="text-lg font-semibold text-green-300 mb-2">
                  <TranslatableText>Medical Emergency</TranslatableText>
                </h3>
                <p className="text-green-200 text-sm mb-3">
                  <TranslatableText>Emergency: 108</TranslatableText>
                </p>
                <div className="flex items-center justify-center text-green-400 text-sm">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                  <TranslatableText>Available 24/7</TranslatableText>
                </div>
              </div>

              {/* Disaster Management */}
              <div className="bg-blue-900/20 backdrop-blur-xl border border-blue-400/30 rounded-2xl p-6 text-center hover:bg-blue-900/30 transition-all duration-300 group cursor-pointer">
                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">🌊</span>
                </div>
                <h3 className="text-lg font-semibold text-blue-300 mb-2">
                  <TranslatableText>Disaster Management</TranslatableText>
                </h3>
                <p className="text-blue-200 text-sm mb-3">
                  <TranslatableText>Emergency: 1070</TranslatableText>
                </p>
                <div className="flex items-center justify-center text-green-400 text-sm">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                  <TranslatableText>Available 24/7</TranslatableText>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Safe Zone Statistics */}
        <section className="py-16 animate-fade-in-up">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-6 bg-gradient-to-r from-green-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
                <TranslatableText>Safe Zone Network Statistics</TranslatableText>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                <TranslatableText>
                  Real-time data on our nationwide network of safe zones and emergency facilities
                </TranslatableText>
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {/* Total Safe Zones */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center hover:bg-black/30 transition-all duration-300 group">
                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">🏠</span>
                </div>
                <div className="text-3xl font-bold text-white mb-2">2,847</div>
                <div className="text-green-400 text-sm font-medium mb-1">
                  <TranslatableText>Total Safe Zones</TranslatableText>
                </div>
                <div className="text-gray-400 text-xs">
                  <TranslatableText>Across India</TranslatableText>
                </div>
              </div>

              {/* Available Capacity */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center hover:bg-black/30 transition-all duration-300 group">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">👥</span>
                </div>
                <div className="text-3xl font-bold text-white mb-2">1.2M</div>
                <div className="text-blue-400 text-sm font-medium mb-1">
                  <TranslatableText>Available Capacity</TranslatableText>
                </div>
                <div className="text-gray-400 text-xs">
                  <TranslatableText>People can be accommodated</TranslatableText>
                </div>
              </div>

              {/* Active Alerts */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center hover:bg-black/30 transition-all duration-300 group">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">⚠️</span>
                </div>
                <div className="text-3xl font-bold text-white mb-2">23</div>
                <div className="text-yellow-400 text-sm font-medium mb-1">
                  <TranslatableText>Active Alerts</TranslatableText>
                </div>
                <div className="text-gray-400 text-xs">
                  <TranslatableText>Weather & Emergency</TranslatableText>
                </div>
              </div>

              {/* Response Time */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center hover:bg-black/30 transition-all duration-300 group">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">⚡</span>
                </div>
                <div className="text-3xl font-bold text-white mb-2">4.2</div>
                <div className="text-purple-400 text-sm font-medium mb-1">
                  <TranslatableText>Avg Response Time</TranslatableText>
                </div>
                <div className="text-gray-400 text-xs">
                  <TranslatableText>Minutes</TranslatableText>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Transportation Hub */}
        <section className="py-16 bg-gradient-to-r from-gray-900/50 to-slate-900/50 animate-fade-in-up">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-6 bg-gradient-to-r from-cyan-400 via-blue-400 to-indigo-400 bg-clip-text text-transparent">
                <TranslatableText>Emergency Transportation Hub</TranslatableText>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                <TranslatableText>
                  Access real-time transportation options and book emergency evacuation services
                </TranslatableText>
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Air Transport */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300 group">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-sky-500 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-xl">✈️</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">
                      <TranslatableText>Air Transport</TranslatableText>
                    </h3>
                    <p className="text-blue-400 text-sm">
                      <TranslatableText>Helicopter & Aircraft</TranslatableText>
                    </p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Available Aircraft:</TranslatableText></span>
                    <span className="text-green-400 font-medium">12</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Response Time:</TranslatableText></span>
                    <span className="text-white font-medium">15-30 min</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Capacity:</TranslatableText></span>
                    <span className="text-white font-medium">4-20 people</span>
                  </div>
                  <button className="w-full mt-4 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors text-sm font-medium">
                    <TranslatableText>Request Air Evacuation</TranslatableText>
                  </button>
                </div>
              </div>

              {/* Ground Transport */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300 group">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-xl">🚌</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">
                      <TranslatableText>Ground Transport</TranslatableText>
                    </h3>
                    <p className="text-green-400 text-sm">
                      <TranslatableText>Buses & Vehicles</TranslatableText>
                    </p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Available Vehicles:</TranslatableText></span>
                    <span className="text-green-400 font-medium">156</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Response Time:</TranslatableText></span>
                    <span className="text-white font-medium">5-15 min</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Capacity:</TranslatableText></span>
                    <span className="text-white font-medium">20-50 people</span>
                  </div>
                  <button className="w-full mt-4 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors text-sm font-medium">
                    <TranslatableText>Request Ground Transport</TranslatableText>
                  </button>
                </div>
              </div>

              {/* Water Transport */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300 group">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-xl">🚤</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">
                      <TranslatableText>Water Transport</TranslatableText>
                    </h3>
                    <p className="text-cyan-400 text-sm">
                      <TranslatableText>Boats & Ships</TranslatableText>
                    </p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Available Boats:</TranslatableText></span>
                    <span className="text-green-400 font-medium">34</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Response Time:</TranslatableText></span>
                    <span className="text-white font-medium">10-25 min</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Capacity:</TranslatableText></span>
                    <span className="text-white font-medium">10-100 people</span>
                  </div>
                  <button className="w-full mt-4 bg-cyan-600 hover:bg-cyan-700 text-white py-2 px-4 rounded-lg transition-colors text-sm font-medium">
                    <TranslatableText>Request Water Transport</TranslatableText>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Resource Centers */}
        <section className="py-16 animate-fade-in-up">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-6 bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 bg-clip-text text-transparent">
                <TranslatableText>Emergency Resource Centers</TranslatableText>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                <TranslatableText>
                  Find essential resources and supplies available at emergency centers near you
                </TranslatableText>
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Food Distribution */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center hover:bg-black/30 transition-all duration-300 group">
                <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">🍽️</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  <TranslatableText>Food Distribution</TranslatableText>
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-300"><TranslatableText>Centers:</TranslatableText></span>
                    <span className="text-orange-400 font-medium">247</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300"><TranslatableText>Meals/Day:</TranslatableText></span>
                    <span className="text-white font-medium">50K+</span>
                  </div>
                  <div className="flex items-center justify-center text-green-400 text-xs mt-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                    <TranslatableText>Active Now</TranslatableText>
                  </div>
                </div>
              </div>

              {/* Medical Supplies */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center hover:bg-black/30 transition-all duration-300 group">
                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">🏥</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  <TranslatableText>Medical Supplies</TranslatableText>
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-300"><TranslatableText>Centers:</TranslatableText></span>
                    <span className="text-green-400 font-medium">189</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300"><TranslatableText>Stock Level:</TranslatableText></span>
                    <span className="text-white font-medium">85%</span>
                  </div>
                  <div className="flex items-center justify-center text-green-400 text-xs mt-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                    <TranslatableText>Well Stocked</TranslatableText>
                  </div>
                </div>
              </div>

              {/* Water Supply */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center hover:bg-black/30 transition-all duration-300 group">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">💧</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  <TranslatableText>Water Supply</TranslatableText>
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-300"><TranslatableText>Centers:</TranslatableText></span>
                    <span className="text-blue-400 font-medium">312</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300"><TranslatableText>Capacity:</TranslatableText></span>
                    <span className="text-white font-medium">2M L</span>
                  </div>
                  <div className="flex items-center justify-center text-green-400 text-xs mt-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                    <TranslatableText>Available</TranslatableText>
                  </div>
                </div>
              </div>

              {/* Shelter & Clothing */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center hover:bg-black/30 transition-all duration-300 group">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">🏠</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  <TranslatableText>Shelter & Clothing</TranslatableText>
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-300"><TranslatableText>Centers:</TranslatableText></span>
                    <span className="text-purple-400 font-medium">156</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300"><TranslatableText>Occupancy:</TranslatableText></span>
                    <span className="text-white font-medium">67%</span>
                  </div>
                  <div className="flex items-center justify-center text-yellow-400 text-xs mt-3">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full mr-2 animate-pulse"></div>
                    <TranslatableText>Limited Space</TranslatableText>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Community Support Network */}
        <section className="py-16 bg-gradient-to-r from-gray-900/50 to-slate-900/50 animate-fade-in-up">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-6 bg-gradient-to-r from-pink-400 via-purple-400 to-indigo-400 bg-clip-text text-transparent">
                <TranslatableText>Community Support Network</TranslatableText>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                <TranslatableText>
                  Connect with volunteers, share resources, and coordinate with your community during emergencies
                </TranslatableText>
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Volunteer Network */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300 group">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-xl">🤝</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">
                      <TranslatableText>Volunteer Network</TranslatableText>
                    </h3>
                    <p className="text-pink-400 text-sm">
                      <TranslatableText>Community Helpers</TranslatableText>
                    </p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Active Volunteers:</TranslatableText></span>
                    <span className="text-green-400 font-medium">1,247</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Response Rate:</TranslatableText></span>
                    <span className="text-white font-medium">94%</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Avg Response:</TranslatableText></span>
                    <span className="text-white font-medium">8 min</span>
                  </div>
                  <button className="w-full mt-4 bg-pink-600 hover:bg-pink-700 text-white py-2 px-4 rounded-lg transition-colors text-sm font-medium">
                    <TranslatableText>Request Volunteer Help</TranslatableText>
                  </button>
                </div>
              </div>

              {/* Resource Sharing */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300 group">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-xl">📦</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">
                      <TranslatableText>Resource Sharing</TranslatableText>
                    </h3>
                    <p className="text-yellow-400 text-sm">
                      <TranslatableText>Community Resources</TranslatableText>
                    </p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Shared Items:</TranslatableText></span>
                    <span className="text-green-400 font-medium">3,456</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Active Sharers:</TranslatableText></span>
                    <span className="text-white font-medium">892</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Success Rate:</TranslatableText></span>
                    <span className="text-white font-medium">96%</span>
                  </div>
                  <button className="w-full mt-4 bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg transition-colors text-sm font-medium">
                    <TranslatableText>Share/Request Resources</TranslatableText>
                  </button>
                </div>
              </div>

              {/* Communication Hub */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300 group">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-xl">💬</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">
                      <TranslatableText>Communication Hub</TranslatableText>
                    </h3>
                    <p className="text-indigo-400 text-sm">
                      <TranslatableText>Community Updates</TranslatableText>
                    </p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Active Groups:</TranslatableText></span>
                    <span className="text-green-400 font-medium">156</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Messages Today:</TranslatableText></span>
                    <span className="text-white font-medium">2,847</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-300"><TranslatableText>Response Time:</TranslatableText></span>
                    <span className="text-white font-medium">2 min</span>
                  </div>
                  <button className="w-full mt-4 bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-lg transition-colors text-sm font-medium">
                    <TranslatableText>Join Community Chat</TranslatableText>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Emergency Alerts & Updates */}
        <section className="py-16 animate-fade-in-up">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-6 bg-gradient-to-r from-red-400 via-orange-400 to-yellow-400 bg-clip-text text-transparent">
                <TranslatableText>Live Emergency Alerts & Updates</TranslatableText>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                <TranslatableText>
                  Stay informed with real-time emergency alerts, weather updates, and safety notifications
                </TranslatableText>
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Active Alerts */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
                <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                  <span className="w-3 h-3 bg-red-500 rounded-full mr-3 animate-pulse"></span>
                  <TranslatableText>Active Emergency Alerts</TranslatableText>
                </h3>
                <div className="space-y-4">
                  <div className="bg-red-900/20 border border-red-400/30 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">🌊</span>
                        <span className="text-red-300 font-medium">Flood Warning</span>
                      </div>
                      <span className="text-xs text-gray-400">2 min ago</span>
                    </div>
                    <p className="text-gray-300 text-sm">
                      <TranslatableText>Heavy rainfall expected in coastal areas. Evacuation recommended for low-lying regions.</TranslatableText>
                    </p>
                    <div className="mt-2 text-xs text-red-400">
                      <TranslatableText>Affected: Mumbai, Pune, Nashik</TranslatableText>
                    </div>
                  </div>

                  <div className="bg-orange-900/20 border border-orange-400/30 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">🔥</span>
                        <span className="text-orange-300 font-medium">Fire Alert</span>
                      </div>
                      <span className="text-xs text-gray-400">15 min ago</span>
                    </div>
                    <p className="text-gray-300 text-sm">
                      <TranslatableText>Forest fire detected in hilly regions. Air quality may be affected.</TranslatableText>
                    </p>
                    <div className="mt-2 text-xs text-orange-400">
                      <TranslatableText>Affected: Shimla, Manali, Dharamshala</TranslatableText>
                    </div>
                  </div>

                  <div className="bg-yellow-900/20 border border-yellow-400/30 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">⚡</span>
                        <span className="text-yellow-300 font-medium">Severe Weather</span>
                      </div>
                      <span className="text-xs text-gray-400">1 hour ago</span>
                    </div>
                    <p className="text-gray-300 text-sm">
                      <TranslatableText>Thunderstorm with strong winds approaching. Secure loose objects.</TranslatableText>
                    </p>
                    <div className="mt-2 text-xs text-yellow-400">
                      <TranslatableText>Affected: Delhi, Gurgaon, Noida</TranslatableText>
                    </div>
                  </div>
                </div>
              </div>

              {/* Safety Updates */}
              <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
                <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                  <span className="w-3 h-3 bg-green-500 rounded-full mr-3 animate-pulse"></span>
                  <TranslatableText>Safety Updates & Tips</TranslatableText>
                </h3>
                <div className="space-y-4">
                  <div className="bg-green-900/20 border border-green-400/30 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">✅</span>
                        <span className="text-green-300 font-medium">Safety Tip</span>
                      </div>
                      <span className="text-xs text-gray-400">5 min ago</span>
                    </div>
                    <p className="text-gray-300 text-sm">
                      <TranslatableText>Keep emergency kit ready with 3-day supply of water, food, and medications.</TranslatableText>
                    </p>
                  </div>

                  <div className="bg-blue-900/20 border border-blue-400/30 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">📱</span>
                        <span className="text-blue-300 font-medium">App Update</span>
                      </div>
                      <span className="text-xs text-gray-400">30 min ago</span>
                    </div>
                    <p className="text-gray-300 text-sm">
                      <TranslatableText>New offline map feature available. Download maps for your area now.</TranslatableText>
                    </p>
                  </div>

                  <div className="bg-purple-900/20 border border-purple-400/30 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">🏥</span>
                        <span className="text-purple-300 font-medium">Health Advisory</span>
                      </div>
                      <span className="text-xs text-gray-400">2 hours ago</span>
                    </div>
                    <p className="text-gray-300 text-sm">
                      <TranslatableText>Ensure you have sufficient medications and know the location of nearest hospitals.</TranslatableText>
                    </p>
                  </div>

                  <button className="w-full mt-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 px-4 rounded-lg transition-all duration-300 font-medium">
                    <TranslatableText>Subscribe to Emergency Alerts</TranslatableText>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}

export default Relocation;
