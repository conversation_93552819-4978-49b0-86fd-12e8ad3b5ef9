@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for ResQTech homepage */
.resqtech-title {
  font-family: 'Inter', sans-serif;
  font-weight: 900;
  letter-spacing: -0.02em;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
}

.resqtech-button {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

.disaster-collage {
  position: relative;
  overflow: hidden;
}

.disaster-collage::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    45deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.leaflet-container {
  width: 100%;
  height: 100%;
  z-index: 1;
  border-radius: 0.5rem;
  position: relative;
  touch-action: pan-x pan-y;
}

.leaflet-control-container .leaflet-control {
  z-index: 999;
}

.leaflet-bottom,
.leaflet-control,
.leaflet-pane,
.leaflet-top {
  z-index: 0 !important;
}

.custom-popup .leaflet-popup-content-wrapper {
  background: white;
  color: #333;
  border-radius: 0.5rem;
  padding: 0;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.custom-popup .leaflet-popup-content {
  margin: 0;
}

.custom-popup .leaflet-popup-tip {
  background: white;
}

.map-tiles {
  filter: saturate(1.2) contrast(1.1);
  mask-mode: alpha;
  mask-repeat: no-repeat;
  mask-size: contain;
  -webkit-mask-mode: alpha;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: contain;
}

/* Add these to your existing styles */
.disaster-marker {
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

.disaster-marker.high {
  background-color: #ef4444;
}

.disaster-marker.moderate {
  background-color: #f59e0b;
}

.disaster-marker.low {
  background-color: #10b981;
}

/* Update existing scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  overflow-y: auto;
  max-height: calc(100vh - 16rem);
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

/* Add custom tooltip styles */
.custom-tooltip {
  background-color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0;
  z-index: 1001 !important;
  width: 300px !important; /* Set fixed width */
  max-width: 300px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.custom-tooltip .leaflet-tooltip-content {
  margin: 0;
  padding: 1rem !important; /* Add padding */
  white-space: normal !important;
}

.custom-tooltip::before {
  display: none;
}

/* Custom tooltip styles */
.custom-tooltip {
  background: white !important;
  border: none !important;
  border-radius: 0.5rem !important;
  padding: 0 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  font-size: 0.875rem !important;
}

.custom-tooltip .leaflet-tooltip-content {
  margin: 0 !important;
}

.custom-tooltip.leaflet-tooltip-top:before {
  border-top-color: white !important;
}

.custom-tooltip.leaflet-tooltip-bottom:before {
  border-bottom-color: white !important;
}

/* Hide attribution outside India bounds */
.leaflet-container .leaflet-control-attribution {
  background-color: transparent !important;
  color: #666;
}

/* Add custom tooltip positioning */
.leaflet-tooltip {
  max-width: 300px;
  white-space: normal !important;
}

/* Popup styles */
.leaflet-popup {
  max-width: 300px !important;
  max-height: 80vh;
  margin-bottom: 0;
}

.leaflet-popup-content-wrapper {
  padding: 0 !important;
  overflow: hidden;
  border-radius: 0.75rem !important;
  max-height: 60vh;
  max-width: 400px !important; /* Increase max width */
  width: 350px !important; /* Set fixed width */
  overflow-y: auto;
  max-height: none !important;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.leaflet-popup-content {
  margin: 8px;
  width: 300px !important;
  width: 100% !important;
  overflow-y: auto;
  max-height: 60vh; /* Limit height */
  padding: 1rem !important; /* Add padding */
  max-height: none !important;
  height: auto;
}

@media (max-width: 640px) {
  .leaflet-popup-content {
    max-width: 280px;
  }
}

/* Ensure popup stays in view */
.leaflet-container {
  position: relative;
}

/* Auto-positioning for popups */
.leaflet-popup-tip-container {
  margin-top: -1px;
  pointer-events: none;
  left: 50%;
  margin-left: -10px;
  width: 20px;
  height: 20px;
}

/* Handle popup overflow */
.leaflet-popup-scrolled {
  border: none;
  overflow: hidden;
}

/* Adjust tooltip arrow */
.leaflet-tooltip-top:before,
.leaflet-tooltip-bottom:before,
.leaflet-tooltip-left:before,
.leaflet-tooltip-right:before {
  border: none !important;
}

/* Improve scrollbar styling */
.leaflet-popup-content-wrapper::-webkit-scrollbar {
  width: 6px;
}

.leaflet-popup-content-wrapper::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.leaflet-popup-content-wrapper::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.leaflet-popup-content-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Update popup positioning styles */
.leaflet-popup {
  max-width: 300px !important;
}

.leaflet-popup-content-wrapper {
  padding: 0;
  overflow: hidden;
  border-radius: 0.5rem;
}

/* Auto-adjust popup tip position */
.leaflet-popup-tip-container {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

/* Top popups */
.leaflet-popup.leaflet-popup-top .leaflet-popup-tip-container {
  top: 100%;
}

/* Bottom popups */
.leaflet-popup.leaflet-popup-bottom .leaflet-popup-tip-container {
  bottom: 100%;
}

/* Custom popup styles */
.custom-popup .leaflet-popup-content-wrapper {
  padding: 0;
  overflow: hidden;
  border-radius: 0.5rem;
}

.custom-popup .leaflet-popup-content {
  margin: 0;
  width: 300px !important;
}

.custom-popup .leaflet-popup-tip {
  background: white;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Add these new styles */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1 1 0%;
}

.min-h-0 {
  min-height: 0;
}

/* Sticky header styles */
.sticky {
  position: sticky;
  background-color: rgb(31, 41, 55);
  margin-top: -0.5rem;
  margin-left: -1.25rem;
  margin-right: -1.25rem;
  padding: 0.5rem 1.25rem;
}

/* Safe zone card styles */
.safe-zone-card {
  background-color: rgb(248, 248, 248);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 0.75rem;
  transition: background-color 0.2s;
}

.safe-zone-card:hover {
  background-color: rgb(75, 85, 99);
}

/* Safe zone card container */
.safe-zones-container {
  overflow-y: auto;
  max-height: calc(100vh - 20rem);
  padding-right: 0.5rem;
}

/* Sticky headers */
.sticky-header {
  position: sticky;
  top: 0;
  background-color: rgb(31, 41, 55);
  padding: 1rem 0;
  z-index: 10;
}

/* Add these styles for better container handling */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

/* Sticky header styles */
.sticky {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Apply background color based on theme */
:root:not(.dark) .sticky {
  background-color: rgb(248, 248, 248);
}

.dark .sticky {
  background-color: rgb(31, 41, 55);
}

/* Container height adjustments */
.h-map {
  height: 600px;
}

.max-h-content {
  max-height: calc(100% - 3rem);
}

/* Safe zone containers */
.safe-zone-container {
  background-color: rgb(31, 41, 55);
  border-radius: 0.75rem;
  margin-bottom: 1rem;
  overflow: hidden;
  border: 1px solid rgba(75, 85, 99, 0.4);
}

/* Safe zone header */
.safe-zone-header {
  padding: 1rem;
  background-color: rgba(55, 65, 81, 0.8);
  border-bottom: 1px solid rgba(75, 85, 99, 0.4);
}

/* Safe zone content */
.safe-zone-content {
  padding: 1rem;
}

/* Safe zone card */
.safe-zone-card {
  background-color: rgb(55, 65, 81);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 0.75rem;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.safe-zone-card:hover {
  background-color: rgb(75, 85, 99);
  border-color: rgba(59, 130, 246, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Transport option cards */
.transport-option {
  background-color: rgb(55, 65, 81);
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  transition: background-color 0.2s;
  border: 1px solid rgba(75, 85, 99, 0.4);
}

.transport-option:hover {
  background-color: rgb(75, 85, 99);
}

.transport-option.recommended {
  border-color: rgba(59, 130, 246, 0.5);
  background-color: rgba(59, 130, 246, 0.1);
}

/* Facility tags */
.facility-tag {
  background-color: #4b5563; /* bg-gray-600 */
  font-size: 0.75rem; /* text-xs */
  color: #e5e7eb; /* text-gray-200 */
  padding: 0.25rem 0.5rem; /* px-2 py-1 */
  border-radius: 9999px; /* rounded-full */
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Safety score indicator */
.safety-score {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
  background-color: rgba(55, 65, 81, 0.5);
}

.score-indicator {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 9999px;
  margin-right: 0.5rem;
}

/* Custom scrollbar for containers */
.safe-zone-scroll {
  max-height: calc(100vh - 16rem);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  padding-right: 0.5rem;
}

/* Section dividers */
.section-divider {
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgba(75, 85, 99, 0.4),
    transparent
  );
  margin: 1rem 0;
}

/* Ensure container fills available space */
.h-full {
  height: 100%;
}

/* Update container height proportions */
.nearest-zone-container {
  max-height: 30%;
  transition: max-height 0.3s ease-in-out;
  overflow: hidden;
}

.nearest-zone-container.hidden {
  max-height: 0;
  margin: 0;
}

.safe-zones-list {
  height: 70%;
  overflow-y: auto;
}

/* Adjust content containers */
.content-container {
  display: flex;
  flex-direction: column;
  height: calc(600px - 2rem);
  gap: 1rem;
}

/* Animations */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Additional utility classes */
.bg-gray-750 {
  background-color: rgba(31, 41, 55, 0.7);
}

.bg-gray-650 {
  background-color: rgba(75, 85, 99, 0.8);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-content {
  background-color: rgb(31, 41, 55);
  border-radius: 0.75rem;
  width: 100%;
  max-width: 36rem;
  overflow: hidden;
  position: relative;
}

/* Remove double scrollbar */
.modal-content::-webkit-scrollbar {
  display: none;
}

/* Container heights */
.h-map-container {
  height: 600px;
}

.h-sidebar {
  height: 600px;
}

/* Scrollable content area */
.content-scroll {
  max-height: calc(100vh - 10rem);
  overflow-y: auto;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-content {
  background-color: rgb(31, 41, 55);
  border-radius: 0.5rem;
  width: 100%;
  max-width: 32rem;
  max-height: 80vh;
  overflow-y: auto;
}

/* Update container height */
.h-map-container {
  height: 600px !important;
  max-height: 600px !important;
}

/* Ensure content fits */
.content-scroll {
  height: calc(600px - 4rem);
  overflow-y: auto;
}

/* Clean up container styles */
.right-sidebar {
  height: 600px;
  display: flex;
  flex-direction: column;
  background-color: rgb(31, 41, 55);
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Modal styles */
.details-modal {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  z-index: 50;
}

.modal-container {
  background-color: #1f2937; /* bg-gray-800 */
  border-radius: 0.75rem; /* rounded-xl */
  width: 100%; /* w-full */
  max-width: 42rem; /* max-w-2xl */
  max-height: 90vh; /* max-h-[90vh] */
  overflow: hidden; /* overflow-hidden */
}

.modal-header {
  position: sticky;
  top: 0;
  background-color: rgb(31, 41, 55);
  border-bottom: 1px solid rgba(75, 85, 99, 0.4);
  padding: 1.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
}

.modal-content {
  overflow-y: auto;
  padding: 1.5rem; /* Tailwind's p-6 equivalent */
  max-height: calc(90vh - 4rem);
}

.info-section {
  background-color: rgb(55, 65, 81);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin: 1rem 0;
}

.transport-card {
  background-color: #374151; /* bg-gray-700 */
  border-radius: 0.5rem; /* rounded-lg */
  padding: 0.75rem; /* p-3 */
  display: flex; /* flex */
  align-items: flex-start; /* items-start */
  gap: 0.75rem; /* gap-3 */
  word-break: break-word;
}

/* Map container styles */
.map-container {
  height: calc(100vh - 14rem);
  min-height: 500px;
  position: relative;
  z-index: 1;
}

/* Right sidebar scroll container */
.sidebar-scroll {
  height: calc(100vh - 16rem);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

/* Footer spacing */
.footer-spacer {
  margin-top: auto;
}

/* Map wrapper */
.leaflet-container {
  height: 100% !important;
  width: 100%;
  border-radius: 0.5rem;
}

/* Dropdown styles */
.location-dropdown {
  position: absolute;
  width: 100%;
  background-color: rgb(55, 65, 81);
  border: 1px solid rgb(75, 85, 99);
  border-top: none;
  border-radius: 0 0 0.5rem 0.5rem;
  max-height: 15rem;
  overflow-y: auto;
  z-index: 50;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.location-dropdown button {
  transition: background-color 0.2s;
}

.location-dropdown button:hover {
  background-color: rgb(75, 85, 99);
}

/* Add this to handle clicking outside dropdown */
.search-overlay {
  position: fixed;
  inset: 0;
  z-index: 40;
}

/* Dropdown styles */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

select option {
  padding: 0.5rem;
  background-color: rgb(55, 65, 81);
  color: white;
}

select option:hover {
  background-color: rgb(75, 85, 99);
}

/* Improved image upload container */
.image-upload-container {
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Community Help Page Styles */
.alert-card {
  background-color: rgb(55, 65, 81);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  transition: all 0.2s;
  border: 1px solid transparent;
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-out;
}

.alert-card:hover {
  border-color: rgba(59, 130, 246, 0.2);
}

.alert-image {
  width: 8rem;
  height: 8rem;
  object-fit: cover;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition-property: transform;
  transition-duration: 200ms;
}

.alert-image:hover {
  transform: scale(1.05);
}

.upvote-button {
  display: flex;
  align-items: center;
  margin-right: 0.5rem;
  padding: 0.375rem 1rem;
  border-radius: 9999px;
  transition-property: all;
  transition-duration: 200ms;
  font-size: 0.875rem;
  font-weight: 500;
}

.form-container {
  background-color: rgba(31, 41, 55, 0.9);
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(55, 65, 81, 0.5);
  backdrop-filter: blur(4px);
}

.input-field {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: rgba(55, 65, 81, 0.9);
  border: 1px solid rgba(75, 85, 99, 0.5);
  transition: box-shadow 0.2s;
}

.input-field:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.input-field:hover {
  border-color: rgba(75, 85, 99, 0.5);
}

.alert-feed {
  gap: 1.5rem;
  max-height: calc(100vh - 12rem);
  overflow-y: auto;
  padding-right: 1rem;
  display: flex;
  flex-direction: column;
}

/* Add to src/index.css */
.input-field[type="select"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
}

/* Enhanced Dropdown Styles */
.select-wrapper {
  position: relative;
}

.select-wrapper::after {
  content: "";
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 0.75rem;
  height: 0.75rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  pointer-events: none;
}

.custom-select {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  appearance: none;
  cursor: pointer;
  width: 100%;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
}

/* Dark mode styles */
.dark .custom-select {
  background-color: rgb(31, 41, 55);
  color: white;
  border: 1px solid rgb(75, 85, 99);
}

/* Light mode styles */
:root:not(.dark) .custom-select {
  background-color: white;
  color: rgb(55, 65, 81);
  border: 1px solid rgb(209, 213, 219);
}

.custom-select:focus {
  outline: none;
  border-color: transparent;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Dark mode hover */
.dark .custom-select:hover {
  background-color: rgb(55, 65, 81);
  border-color: rgb(96, 165, 250);
}

/* Light mode hover */
:root:not(.dark) .custom-select:hover {
  background-color: rgb(243, 244, 246);
  border-color: rgb(96, 165, 250);
}

/* Dark mode options */
.dark .custom-select option {
  background-color: rgb(31, 41, 55);
  color: white;
  padding: 0.5rem 0;
}

.dark .custom-select option:hover,
.dark .custom-select option:focus,
.dark .custom-select option:checked {
  background-color: rgb(55, 65, 81);
}

/* Light mode options */
:root:not(.dark) .custom-select option {
  background-color: white;
  color: rgb(55, 65, 81);
  padding: 0.5rem 0;
}

:root:not(.dark) .custom-select option:hover,
:root:not(.dark) .custom-select option:focus,
:root:not(.dark) .custom-select option:checked {
  background-color: rgb(243, 244, 246);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Dark mode form label */
.dark .form-label {
  color: #e5e7eb; /* Lighter text for better contrast in dark mode */
}

/* Light mode form label */
:root:not(.dark) .form-label {
  color: #4b5563; /* Gray-600 for good contrast in light mode */
}

/* Alert Card Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Alert Card Styles */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.alert-card {
  animation: fadeIn 0.3s ease-out;
  backdrop-filter: blur(8px);
}

/* Custom Scrollbar */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(59, 130, 246, 0.5);
  border-radius: 3px;
}

/* Hover Effects */
.hover-scale {
  transition: transform 0.2s;
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Mitigation Page Styles */
.mitigation-card {
  background-color: rgba(31, 41, 55, 0.9);
  backdrop-filter: blur(4px);
  border-radius: 0.5rem;
  padding: 1.5rem;
  transition: all 0.2s;
  cursor: pointer;
  border: 1px solid rgba(55, 65, 81, 0.5);
}

.mitigation-card:hover {
  background-color: rgba(55, 65, 81, 0.9);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
}

.category-btn {
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  transition: color 0.2s;
  font-weight: 500;
}

.category-btn.active {
  background-color: #2563eb;
  color: white;
  box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.5);
}

.category-btn.inactive {
  background-color: rgb(31, 41, 55);
  color: #d1d5db;
}

.category-btn.inactive:hover {
  background-color: rgb(55, 65, 81);
}

.search-bar {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: rgb(31, 41, 55);
  border-radius: 0.5rem;
  border: 1px solid rgb(55, 65, 81);
  color: white;
  transition: all 0.2s;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.search-bar:focus {
  outline: none;
  outline: 2px solid rgb(59, 130, 246);
  border-color: transparent;
}

.tab-button {
  flex: 1 1 0%;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
}

.guide-modal {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  z-index: 50;
}

.progress-bar {
  height: 0.25rem;
  background-color: rgb(55, 65, 81);
  border-radius: 9999px;
  overflow: hidden;
  position: relative;
  margin-top: 0.5rem;
}

.progress-value {
  height: 100%;
  background-color: #3b82f6;
  transition-property: all;
  transition-duration: 300ms;
  position: absolute;
  left: 0;
  top: 0;
}

/* Modal styles */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.5) rgba(17, 24, 39, 0.3);
}

.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(17, 24, 39, 0.3);
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}

.leaflet-popup-close-button {
  right: 8px !important;
  top: 8px !important;
  color: #4b5563 !important;
  transition: color 0.2s;
}

.leaflet-popup-close-button:hover {
  color: #1f2937 !important;
}

/* Update popup container styles */
.leaflet-popup-content-wrapper {
  padding: 0 !important;
  border-radius: 0.75rem !important;
  overflow: hidden !important;
  margin: 0 !important;
  background: transparent !important;
}

.leaflet-popup-content {
  margin: 0 !important;
  width: 350px !important;
}

.leaflet-popup {
  margin-bottom: 0 !important;
}

.leaflet-popup-tip {
  background: #1f2937 !important; /* Match the dark theme */
}

/* Custom scrollbar for popup */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) rgba(31, 41, 55, 0.3);
}

.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.3);
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Custom scrollbar for popup content */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Mobile and Responsive Styles */
@media (max-width: 768px) {
  .responsive-container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .modal-content {
    width: 90%;
    margin: 1rem;
  }

  .grid-cols-3 {
    grid-template-columns: 1fr;
  }

  .md\:pl-64 {
    padding-left: 1rem;
  }
}

/* Container styles */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

/* Card styles */
.responsive-card {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Form styles */
.responsive-form {
  max-width: 100%;
  width: 100%;
}

.responsive-input {
  width: 100%;
  padding: 0.5rem;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
}

/* Button styles */
.responsive-button {
  width: 100%;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  background-color: #3b82f6;
  color: white;
  font-weight: 500;
}

/* Navigation styles */
.responsive-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: white;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 50;
}

@media (min-width: 768px) {
  .responsive-nav {
    width: 16rem;
    height: 100vh;
  }
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.modal-content {
  background-color: white;
  padding: 1rem;
  border-radius: 0.5rem;
  max-width: 90%;
  width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

/* Scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}
