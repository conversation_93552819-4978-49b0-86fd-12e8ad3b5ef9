{"name": "resqtech", "version": "1.0.0", "description": "Disaster Management System", "main": "index.js", "private": true, "dependencies": {"@firebase/auth": "^1.9.1", "@heroicons/react": "^2.2.0", "@tailwindcss/forms": "^0.5.10", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.2", "browser-image-compression": "^2.0.2", "date-fns": "^4.1.0", "firebase": "^11.4.0", "framer-motion": "^12.5.0", "leaflet": "^1.9.4", "lucide-react": "^0.483.0", "nodemailer": "^6.10.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-firebase-hooks": "^5.1.1", "react-icons": "^5.5.0", "react-leaflet": "^4.2.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.30.0", "react-scripts": "5.0.1", "react-share": "^5.2.2", "twilio": "^5.5.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "dev": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5"}}